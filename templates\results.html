{% extends 'layout.html' %}

{% block title %}CIN Extractor - Results{% endblock %}

{% block content %}
<div class="container py-4 py-md-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white p-3">
                    <h2 class="text-center mb-0 d-flex align-items-center justify-content-center">
                        <i class="bi bi-check-circle me-2"></i>
                        <span>CIN Extraction Results</span>
                    </h2>
                </div>
                <div class="card-body p-0">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show m-3" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close touch-friendly" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                        
                    <div class="p-3 p-md-4">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
                            {% if is_processing %}
                            <h4 class="mb-0 d-flex align-items-center">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Processing in Progress
                            </h4>
                            <button class="btn btn-primary w-100 w-md-auto touch-friendly" disabled>
                                <i class="bi bi-hourglass-split me-2"></i>
                                Processing...
                            </button>
                            {% else %}
                            <h4 class="mb-0">Processing Complete</h4>
                            <a href="{{ url_for('download') }}" class="btn btn-primary w-100 w-md-auto touch-friendly">
                                <i class="bi bi-download me-2"></i>
                                Download CSV
                            </a>
                            {% endif %}
                        </div>
                        
                        {% if is_processing and task_id %}
                        <div class="alert alert-info mb-4">
                            <p>Your file is being processed in the background. This page will automatically refresh when processing is complete.</p>
                            <div class="progress mt-3" style="height: 10px;">
                                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 50%"></div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Stats cards (more mobile-friendly layout) -->
                        <div class="row row-cols-2 row-cols-md-4 g-3 mb-4">
                            <div class="col">
                                <div class="card bg-dark text-center h-100 p-3">
                                    <h3 class="mb-2">{{ stats.total }}</h3>
                                    <p class="mb-0 small">Total Companies</p>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card bg-success bg-opacity-25 text-center h-100 p-3">
                                    <h3 class="mb-2">{{ stats.success }}</h3>
                                    <p class="mb-0 small">Found</p>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card bg-warning bg-opacity-25 text-center h-100 p-3">
                                    <h3 class="mb-2">{{ stats.not_found }}</h3>
                                    <p class="mb-0 small">Not Found</p>
                                </div>
                            </div>
                            <div class="col">
                                <div class="card bg-danger bg-opacity-25 text-center h-100 p-3">
                                    <h3 class="mb-2">{{ stats.error }}</h3>
                                    <p class="mb-0 small">Errors</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tabs for mobile-friendly navigation -->
                        <ul class="nav nav-tabs mb-3 d-md-none" id="resultsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active touch-friendly" id="table-tab" data-bs-toggle="tab" data-bs-target="#table-content" type="button" role="tab" aria-controls="table-content" aria-selected="true">
                                    <i class="bi bi-table me-1"></i> Table
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link touch-friendly" id="card-tab" data-bs-toggle="tab" data-bs-target="#card-content" type="button" role="tab" aria-controls="card-content" aria-selected="false">
                                    <i class="bi bi-card-list me-1"></i> Cards
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content">
                            <!-- Table view (better for desktop and landscape tablet) -->
                            <div class="tab-pane fade show active" id="table-content" role="tabpanel" aria-labelledby="table-tab">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Company Name</th>
                                                <th>CIN</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for result in results %}
                                            <tr>
                                                <td>{{ loop.index }}</td>
                                                <td>{{ result.company_name }}</td>
                                                <td>
                                                    {% if result.cin %}
                                                        <code>{{ result.cin }}</code>
                                                    {% else %}
                                                        <span class="text-muted">-</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if result.status == 'Success' %}
                                                        <span class="badge bg-success">Success</span>
                                                    {% elif result.status == 'Not found' %}
                                                        <span class="badge bg-warning">Not found</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">Error</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Card view (better for mobile) -->
                            <div class="tab-pane fade d-md-none" id="card-content" role="tabpanel" aria-labelledby="card-tab">
                                <div class="row g-3">
                                    {% for result in results %}
                                    <div class="col-12">
                                        <div class="card h-100 p-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-0">{{ result.company_name }}</h6>
                                                <span class="ms-2">
                                                    {% if result.status == 'Success' %}
                                                        <span class="badge bg-success">Success</span>
                                                    {% elif result.status == 'Not found' %}
                                                        <span class="badge bg-warning">Not found</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">Error</span>
                                                    {% endif %}
                                                </span>
                                            </div>
                                            {% if result.cin %}
                                                <div class="mt-2">
                                                    <div class="text-muted small">CIN:</div>
                                                    <code>{{ result.cin }}</code>
                                                </div>
                                            {% else %}
                                                <div class="text-muted mt-2">No CIN found</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Bottom button panel for touch devices -->
                        <div class="button-panel mt-4">
                            <div class="row g-3">
                                <div class="col-12 col-md-6">
                                    <a href="{{ url_for('index') }}" class="btn btn-secondary w-100 touch-friendly">
                                        <i class="bi bi-arrow-left me-2"></i>
                                        Upload Another File
                                    </a>
                                </div>
                                <div class="col-12 col-md-6">
                                    <a href="{{ url_for('download') }}" class="btn btn-primary w-100 touch-friendly">
                                        <i class="bi bi-download me-2"></i>
                                        Download CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center text-muted p-3">
                    <small>Processed file: {{ filename }}</small>
                    <div class="mt-1">
                        <small class="text-muted">This tool uses automatic search engine rotation between Yahoo and Bing with human-like behavior to extract CIN numbers.</small>
                        <div class="mt-1">
                            <small class="text-muted">Created by Prashant</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{% if is_processing and task_id %}
<script>
    // Function to check task status
    function checkTaskStatus() {
        fetch('/task_status/{{ task_id }}')
            .then(response => response.json())
            .then(data => {
                console.log('Task status:', data);
                if (data.status === 'completed') {
                    // Show completion animation
                    document.getElementById('progress-bar').style.width = '100%';
                    // Add a slight delay before reload for better UX
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            })
            .catch(error => {
                console.error('Error checking task status:', error);
            });
    }

    // Check status every 5 seconds
    const statusChecker = setInterval(checkTaskStatus, 5000);
    
    // Pulse animation for waiting state
    let pulseDirection = 1;
    let pulseValue = 50;
    setInterval(() => {
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            pulseValue += (pulseDirection * 5);
            if (pulseValue >= 75) {
                pulseDirection = -1;
            } else if (pulseValue <= 25) {
                pulseDirection = 1;
            }
            progressBar.style.width = `${pulseValue}%`;
        }
    }, 1000);
    
    // Add touch-friendly behavior to tabs
    document.addEventListener('DOMContentLoaded', function() {
        // Enable horizontal swipe between tabs on mobile
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            const tabContainer = document.querySelector('.tab-content');
            if (tabContainer) {
                const hammer = new Hammer(tabContainer);
                hammer.on('swipeleft', function() {
                    // Swipe left to go to cards view
                    document.getElementById('card-tab').click();
                });
                hammer.on('swiperight', function() {
                    // Swipe right to go to table view
                    document.getElementById('table-tab').click();
                });
            }
        }
    });
</script>
{% endif %}
{% endblock %}
