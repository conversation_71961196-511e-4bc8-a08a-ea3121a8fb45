{% extends 'layout.html' %}

{% block title %}LinkedIn Extraction Results{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-light">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>LinkedIn Profile Extraction Results</h3>
                    <div>
                        <a href="{{ url_for('linkedin_extractor') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Extractor
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h4 class="alert-heading"><i class="fas fa-check-circle"></i> Extraction Complete!</h4>
                        <p>Your LinkedIn profile extraction has been completed. You can download the results or view them below.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info bg-gradient text-white mb-3">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Processed</h5>
                                    <p class="card-text display-4">{{ stats.total }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success bg-gradient text-white mb-3">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Profiles Found</h5>
                                    <p class="card-text display-4">{{ stats.success }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning bg-gradient text-dark mb-3">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Not Found</h5>
                                    <p class="card-text display-4">{{ stats.not_found }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger bg-gradient text-white mb-3">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Errors</h5>
                                    <p class="card-text display-4">{{ stats.error }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mb-4">
                        <a href="{{ url_for('download_linkedin_results') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-download me-2"></i> Download CSV
                        </a>
                    </div>
                    
                    <div class="card bg-dark border-secondary">
                        <div class="card-header bg-secondary">
                            <h5 class="mb-0">Results Preview</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-dark table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Person Name</th>
                                            <th>Company</th>
                                            <th>LinkedIn URL</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for result in results %}
                                        <tr>
                                            <td>{{ result['Person Name'] }}</td>
                                            <td>{{ result['Current Company Name'] }}</td>
                                            <td>
                                                {% if result['LinkedIn URL'] %}
                                                <a href="{{ result['LinkedIn URL'] }}" target="_blank" class="text-info">
                                                    {{ result['LinkedIn URL']|truncate(30) }}
                                                    <i class="fas fa-external-link-alt ms-1"></i>
                                                </a>
                                                {% else %}
                                                <span class="text-muted">—</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if result['Processing Status'] == 'Success' %}
                                                <span class="badge bg-success">Success</span>
                                                {% elif result['Processing Status'] == 'Not Found' %}
                                                <span class="badge bg-warning text-dark">Not Found</span>
                                                {% elif result['Processing Status'].startswith('Error') %}
                                                <span class="badge bg-danger" title="{{ result['Processing Status'] }}">
                                                    Error <i class="fas fa-info-circle"></i>
                                                </span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ result['Processing Status'] }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="4" class="text-center">No results found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-muted text-center">
                    Created by Prashant
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}