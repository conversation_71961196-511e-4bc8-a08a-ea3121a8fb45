"""
Simplified version of the CIN Extractor app for Netlify deployment
This version removes heavy dependencies and focuses on core functionality
"""
import os
import logging
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = "cin-extractor-netlify-key-20250716"

# Basic configuration for Netlify
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

@app.route('/')
def index():
    """Home page"""
    return render_template('home.html')

@app.route('/cin-extractor')
def cin_extractor():
    """CIN Extractor page"""
    return render_template('index.html')

@app.route('/linkedin-extractor')
def linkedin_extractor():
    """LinkedIn Extractor page"""
    return render_template('linkedin_extractor.html')

@app.route('/history')
def task_history():
    """Task history page"""
    return render_template('history.html', tasks=[])

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload - simplified version"""
    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('cin_extractor'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('cin_extractor'))
    
    # For now, just show a message that the feature is being set up
    flash('File upload feature is being configured for Netlify deployment. Please check back soon!', 'info')
    return redirect(url_for('cin_extractor'))

@app.route('/upload-linkedin', methods=['POST'])
def upload_linkedin_file():
    """Handle LinkedIn file upload - simplified version"""
    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # For now, just show a message that the feature is being set up
    flash('LinkedIn extraction feature is being configured for Netlify deployment. Please check back soon!', 'info')
    return redirect(url_for('linkedin_extractor'))

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'CIN Extractor is running on Netlify',
        'version': '1.0.0-netlify'
    })

@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f'Server Error: {error}')
    return render_template('500.html'), 500

if __name__ == '__main__':
    # This won't be used in Netlify Functions, but useful for local testing
    app.run(debug=True, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
