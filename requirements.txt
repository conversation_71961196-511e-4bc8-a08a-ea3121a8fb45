# Core Flask dependencies
flask>=3.1.0
flask-sqlalchemy>=3.1.1
werkzeug>=3.1.3

# Data processing
pandas>=2.2.3
requests>=2.32.3
beautifulsoup4>=4.13.3

# Web scraping (lightweight alternatives for serverless)
trafilatura>=2.0.0

# Validation
email-validator>=2.2.0

# Database (SQLite for serverless, PostgreSQL for production)
# psycopg2-binary>=2.9.10  # Commented out for serverless deployment

# Note: Selenium and webdriver-manager are too heavy for serverless
# selenium>=4.30.0
# webdriver-manager>=4.0.2

# Server (needed for Render deployment)
gunicorn>=23.0.0
