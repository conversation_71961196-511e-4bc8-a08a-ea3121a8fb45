<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#212529">
    <meta name="description" content="Extract data using our web-based tools">
    <title>{% block title %}Data Extraction Tools{% endblock %}</title>
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.css">
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='icons/favicon.png') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='icons/apple-icon-180.png') }}">
    {% block head %}{% endblock %}
    {% block extra_head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand py-2" href="{{ url_for('index') }}">
                <i class="bi bi-tools me-2"></i>
                Data Extraction Tools
            </a>
            <button class="navbar-toggler touch-friendly" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link touch-friendly py-3" href="{{ url_for('cin_extractor') }}">
                            <i class="bi bi-search me-1"></i>
                            CIN Extractor
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link touch-friendly py-3" href="{{ url_for('linkedin_extractor') }}">
                            <i class="bi bi-linkedin me-1"></i>
                            LinkedIn Extractor
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link touch-friendly py-3" href="{{ url_for('task_history') }}">
                            <i class="bi bi-clock-history me-1"></i>
                            History
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div id="mobile-swipe-container">
        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-center text-white-50 py-3 mt-5">
        <div class="container">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center">
                <p class="mb-2 mb-md-0">
                    &copy; 2025 Data Extraction Tools
                </p>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('index') }}" class="text-white-50 touch-friendly-icon">
                        <i class="bi bi-house-door fs-5"></i>
                    </a>
                    <a href="{{ url_for('cin_extractor') }}" class="text-white-50 touch-friendly-icon">
                        <i class="bi bi-search fs-5"></i>
                    </a>
                    <a href="{{ url_for('linkedin_extractor') }}" class="text-white-50 touch-friendly-icon">
                        <i class="bi bi-linkedin fs-5"></i>
                    </a>
                    <a href="{{ url_for('task_history') }}" class="text-white-50 touch-friendly-icon">
                        <i class="bi bi-clock-history fs-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Core scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    
    <!-- Mobile touch gestures -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Hammer.js for swipe gestures if on a touch device
            if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                const swipeContainer = document.getElementById('mobile-swipe-container');
                if (swipeContainer) {
                    const hammer = new Hammer(swipeContainer);
                    hammer.on('swiperight', function() {
                        // Swipe right to go to home
                        window.location.href = "{{ url_for('index') }}";
                    });
                }
            }
            
            // Add active state for better touch feedback
            const touchElements = document.querySelectorAll('.touch-friendly, .touch-friendly-icon, .btn');
            touchElements.forEach(el => {
                el.addEventListener('touchstart', function() {
                    this.classList.add('touch-active');
                });
                el.addEventListener('touchend', function() {
                    this.classList.remove('touch-active');
                });
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
    {% block extra_scripts %}{% endblock %}
</body>
</html>