{% extends 'layout.html' %}

{% block title %}LinkedIn Data Processing{% endblock %}

{% block extra_head %}
<meta http-equiv="refresh" content="10">
<style>
    .progress-bar.progress-bar-striped.progress-bar-animated {
        transition: width 1s ease;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(13, 202, 240, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(13, 202, 240, 0); }
        100% { box-shadow: 0 0 0 0 rgba(13, 202, 240, 0); }
    }
    
    .counter-value {
        animation: pulse 2s infinite;
        border-radius: 50%;
        padding: 0.5rem 1rem;
        background: rgba(13, 202, 240, 0.2);
        display: inline-block;
    }
    
    .countdown-timer {
        font-size: 2rem;
        font-weight: bold;
        color: #f8f9fa;
    }
    
    .card-stats {
        transition: all 0.3s ease;
    }
    
    .card-stats:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-light">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>LinkedIn Profile Extraction in Progress</h3>
                    <div>
                        <a href="{{ url_for('linkedin_extractor') }}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                        {% if interrupted %}
                        <a href="{{ url_for('resume_linkedin_task', task_id=task_id) }}" class="btn btn-success">
                            <i class="fas fa-play-circle"></i> Resume Processing
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div id="status-container">
                        {% if interrupted %}
                        <div class="alert alert-warning">
                            <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Processing Paused</h4>
                            <p>The LinkedIn profile extraction has been temporarily paused to avoid detection.</p>
                            <p>You can resume processing by clicking the "Resume Processing" button. Alternatively, processing will resume automatically in <span id="resume-countdown" class="countdown-timer">--:--</span>.</p>
                            <div class="progress mt-3">
                                <div id="cooldown-progress" class="progress-bar progress-bar-striped progress-bar-animated bg-info" style="width: 0%"></div>
                            </div>
                        </div>
                        {% else %}
                        <div id="processing-alert" class="alert alert-info">
                            <h4 class="alert-heading"><i class="fas fa-sync fa-spin"></i> Extracting LinkedIn Profiles</h4>
                            <p>Your file is being processed in the background. This page will refresh automatically to show progress.</p>
                            <p>You can safely close this page and return later - progress is saved automatically.</p>
                        </div>
                        {% endif %}
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary bg-gradient text-white card-stats">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Processed</h5>
                                    <p class="card-text display-4"><span id="processed-count" class="counter-value">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success bg-gradient text-white card-stats">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Found</h5>
                                    <p class="card-text display-4"><span id="success-count" class="counter-value">0</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger bg-gradient text-white card-stats">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Not Found</h5>
                                    <p class="card-text display-4"><span id="failed-count" class="counter-value">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-dark border-secondary mb-4">
                        <div class="card-header bg-secondary">
                            <h5 class="mb-0">Progress</h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3" style="height: 25px;">
                                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">0%</div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>Current:</strong> <span id="current-company">-</span>
                                </div>
                                <div>
                                    <strong>Remaining Time:</strong> <span id="remaining-time">Calculating...</span>
                                </div>
                                <div>
                                    <strong>ETA:</strong> <span id="eta">Calculating...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if task_has_partial_results %}
                    <div class="text-center mt-4">
                        <a href="{{ url_for('download_linkedin_partial', task_id=task_id) }}" class="btn btn-info btn-lg">
                            <i class="fas fa-download me-2"></i> Download Current Results
                        </a>
                        <p class="text-muted mt-2"><small>Download the data processed so far</small></p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted text-center">
                    Created by Prashant
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    function updateStatus() {
        fetch('{{ url_for("linkedin_task_status", task_id=task_id) }}')
            .then(response => response.json())
            .then(data => {
                // Update progress
                if (data.progress_percentage !== undefined) {
                    const progressBar = document.getElementById('progress-bar');
                    progressBar.style.width = data.progress_percentage + '%';
                    progressBar.innerText = data.progress_percentage + '%';
                }
                
                // Update processed count
                if (data.processed_companies !== undefined) {
                    document.getElementById('processed-count').innerText = data.processed_companies;
                }
                
                // Update success/failure counts
                if (data.success_count !== undefined) {
                    document.getElementById('success-count').innerText = data.success_count;
                }
                
                if (data.failure_count !== undefined) {
                    document.getElementById('failed-count').innerText = data.failure_count;
                }
                
                // Update current company
                if (data.current_company) {
                    document.getElementById('current-company').innerText = data.current_company;
                }
                
                // Update time estimates
                if (data.remaining_time_formatted) {
                    document.getElementById('remaining-time').innerText = data.remaining_time_formatted;
                }
                
                if (data.estimated_completion_time) {
                    const eta = new Date(data.estimated_completion_time);
                    document.getElementById('eta').innerText = eta.toLocaleTimeString();
                }
                
                // Handle task completion
                if (data.status === 'completed') {
                    document.getElementById('status-container').innerHTML = `
                        <div class="alert alert-success">
                            <h4 class="alert-heading"><i class="fas fa-check-circle"></i> Processing Complete!</h4>
                            <p>All LinkedIn profiles have been processed successfully.</p>
                            <a href="{{ url_for('linkedin_results') }}" class="btn btn-success">View Results</a>
                        </div>
                    `;
                    
                    // Stop auto-refresh
                    const meta = document.querySelector('meta[http-equiv="refresh"]');
                    if (meta) meta.remove();
                }
                
                // Handle interruption
                if (data.status === 'interrupted') {
                    const statusContainer = document.getElementById('status-container');
                    if (!statusContainer.querySelector('.alert-warning')) {
                        statusContainer.innerHTML = `
                            <div class="alert alert-warning">
                                <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Processing Paused</h4>
                                <p>The LinkedIn profile extraction has been temporarily paused to avoid detection.</p>
                                <p>You can resume processing by clicking the "Resume Processing" button. Alternatively, processing will resume automatically in <span id="resume-countdown" class="countdown-timer">--:--</span>.</p>
                                <div class="progress mt-3">
                                    <div id="cooldown-progress" class="progress-bar progress-bar-striped progress-bar-animated bg-info" style="width: 0%"></div>
                                </div>
                            </div>
                        `;
                    }
                    
                    // Update cooldown countdown
                    if (data.cooldown_remaining !== undefined) {
                        const minutes = Math.floor(data.cooldown_remaining / 60);
                        const seconds = data.cooldown_remaining % 60;
                        document.getElementById('resume-countdown').innerText = 
                            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                        
                        // Update progress bar
                        if (data.resume_after !== undefined) {
                            const percentage = (1 - (data.cooldown_remaining / data.resume_after)) * 100;
                            document.getElementById('cooldown-progress').style.width = percentage + '%';
                        }
                    }
                    
                    // Auto-reload if it's time to resume
                    if (data.can_resume) {
                        window.location.href = '{{ url_for("resume_linkedin_task", task_id=task_id) }}';
                    }
                }
                
                // Handle errors
                if (data.status === 'error') {
                    document.getElementById('status-container').innerHTML = `
                        <div class="alert alert-danger">
                            <h4 class="alert-heading"><i class="fas fa-exclamation-circle"></i> Processing Error</h4>
                            <p>An error occurred: ${data.error || 'Unknown error'}</p>
                            <a href="{{ url_for('linkedin_extractor') }}" class="btn btn-primary">Try Again</a>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching status:', error);
            });
    }
    
    // Update status immediately
    updateStatus();
    
    // Then update every 5 seconds
    setInterval(updateStatus, 5000);
});
</script>
{% endblock %}