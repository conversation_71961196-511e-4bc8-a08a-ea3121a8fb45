document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for file input change
    const fileInput = document.getElementById('file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const fileName = this.files[0]?.name;
            if (fileName) {
                // Validate file extension
                const extension = fileName.split('.').pop().toLowerCase();
                if (extension !== 'csv') {
                    alert('Please select a CSV file');
                    this.value = '';
                }
            }
        });
    }
    
    // Initialize all bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add event listener for form submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(event) {
            const fileInput = document.getElementById('file');
            if (!fileInput.files.length) {
                event.preventDefault();
                alert('Please select a file to upload');
                return false;
            }
            
            // Show loading state
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...';
            }
            
            return true;
        });
    }
});
