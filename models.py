from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Company(db.Model):
    """Model for storing all processed companies and their CINs and turnover data"""
    id = db.Column(db.Integer, primary_key=True)
    company_name = db.Column(db.String(255), nullable=False)
    cin = db.Column(db.String(21), nullable=True)  # CIN is 21 characters
    turnover = db.Column(db.String(50), nullable=True)  # Company turnover value (e.g., "500+")
    cin_status = db.Column(db.String(50), nullable=True)  # Success, Not found, Error for CIN
    turnover_status = db.Column(db.String(50), nullable=True)  # Success, Not found, Error for turnover
    status = db.Column(db.String(50), nullable=True)  # Overall status
    source_file = db.Column(db.String(255), nullable=True)  # Original file name
    user_ip = db.Column(db.String(50), nullable=True)  # IP address of uploader
    processed_at = db.Column(db.DateTime, default=datetime.utcnow)
    extraction_type = db.Column(db.String(10), nullable=True)  # cin, turnover, or both
    
    def __repr__(self):
        if self.turnover:
            return f'<Company {self.company_name} - CIN: {self.cin}, Turnover: {self.turnover}>'
        return f'<Company {self.company_name} - CIN: {self.cin}>'