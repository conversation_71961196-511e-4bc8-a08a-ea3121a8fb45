[build]
  command = "pip install -r requirements.txt"
  functions = "netlify/functions"
  publish = "static"

[build.environment]
  PYTHON_VERSION = "3.11"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/app/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/.netlify/functions/app/:splat"
  status = 200

[functions]
  directory = "netlify/functions"

[functions."app"]
  runtime = "python3.11"
