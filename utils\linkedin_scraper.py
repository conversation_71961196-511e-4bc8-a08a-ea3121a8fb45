import os
import time
import random
import pandas as pd
import json
import threading
import logging
import traceback
import requests
import urllib.parse
from datetime import datetime, timedelta

# Setup logging
logger = logging.getLogger(__name__)

# Try importing Selenium - we'll handle the case if it fails in the Replit environment
SELENIUM_AVAILABLE = True
try:
    # Selenium imports
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, StaleElementReferenceException
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.warning("Selenium not available - will use alternative search methods")

from flask import current_app


class LinkedInScraper:
    def __init__(self, headless=True, manual_setup=False):
        """Initialize LinkedIn scraper with optional headless mode"""
        self.headless = headless
        self.manual_setup = manual_setup
        self.driver = None
        # Only use Yahoo and Bing as search engines
        self.search_engines = [
            {"name": "Yahoo", "url": "https://search.yahoo.com/search?p=", "visible": True},
            {"name": "Bing", "url": "https://www.bing.com/search?q=", "visible": True}
        ]
        self.current_search_engine = 0
        self.search_count = 0
        self.max_searches_per_engine = 50  # Switch after 50 searches to reduce detection
        
        # Flag to track if selenium is working
        self.selenium_mode = SELENIUM_AVAILABLE
        
        # Setup requests session for fallback mode
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Referer': 'https://www.google.com/'
        })
        
        # Try to set up the driver if Selenium is available
        if self.selenium_mode:
            try:
                self.setup_driver()
            except Exception as e:
                logger.error(f"Failed to initialize Selenium: {e}")
                self.selenium_mode = False
                logger.warning("Falling back to requests-based search method")
    
    def setup_driver(self):
        """Setup Chrome driver with advanced anti-detection measures for Replit environment"""
        options = Options()
        
        if self.headless and not self.manual_setup:
            options.add_argument("--headless=new")  # Using new headless mode for better compatibility
        
        # Essential options for Replit environment
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-extensions")
        
        # Advanced anti-bot detection measures (similar to provided code)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-infobars")
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--window-size=1920,1080")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        
        # Add random user agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Edge/121.0.0.0"
        ]
        options.add_argument(f"user-agent={random.choice(user_agents)}")
        
        # Add language and other preferences to appear human
        options.add_argument("--lang=en-US,en;q=0.9")
        options.add_argument("--accept-language=en-US,en;q=0.9")
        
        # Create incognito window to avoid cookies and tracking
        options.add_argument("--incognito")
        
        # Handle Chrome and ChromeDriver setup for Replit environment
        for attempt in range(3):  # Try 3 different methods
            try:
                if attempt == 0:
                    # First approach: Use selenium's built-in Service directly
                    logger.info("Attempting to initialize ChromeDriver with direct method...")
                    self.driver = webdriver.Chrome(options=options)
                elif attempt == 1:
                    # Second approach: Use minimal options for highest compatibility
                    logger.info("Attempting ChromeDriver with minimal options...")
                    minimal_options = Options()
                    minimal_options.add_argument("--headless=new")
                    minimal_options.add_argument("--no-sandbox")
                    minimal_options.add_argument("--disable-dev-shm-usage")
                    minimal_options.add_argument("--disable-gpu")
                    self.driver = webdriver.Chrome(options=minimal_options)
                else:
                    # Third approach: Try with ChromeDriverManager
                    logger.info("Attempting ChromeDriver with ChromeDriverManager...")
                    from webdriver_manager.chrome import ChromeDriverManager
                    from selenium.webdriver.chrome.service import Service
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=options)
                
                # If we get here, driver initialized successfully
                self.driver.set_page_load_timeout(30)
                
                # Apply anti-fingerprinting measures (from provided code)
                self.driver.execute_script(
                    "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                
                # Additional anti-bot fingerprinting from provided code
                self.driver.execute_script(
                    "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
                
                logger.info(f"ChromeDriver initialized successfully with method {attempt+1}")
                break  # Break out of the loop if successful
                
            except Exception as e:
                logger.error(f"ChromeDriver initialization attempt {attempt+1} failed: {e}")
                # If on final attempt, raise the error
                if attempt == 2:
                    raise RuntimeError("Could not initialize ChromeDriver with any method") from e
    
    def human_delay(self):
        """Generate a human-like delay between operations"""
        # Base delay between 2-5 seconds
        base_delay = random.uniform(2, 5)
        
        # Occasionally add longer pauses (15% chance)
        if random.random() < 0.15:
            base_delay += random.uniform(3, 8)
        
        time.sleep(base_delay)
    
    def rotate_search_engine(self):
        """Rotate to the next search engine"""
        self.search_count += 1
        
        # Check if it's time to switch search engines
        if self.search_count >= self.max_searches_per_engine:
            self.current_search_engine = (self.current_search_engine + 1) % len(self.search_engines)
            self.search_count = 0
            logger.info(f"Rotating to search engine: {self.search_engines[self.current_search_engine]['name']}")
    
    def fallback_search_profile(self, name, company, designation=""):
        """
        Fallback method to search for LinkedIn profiles using requests instead of Selenium
        This is used when Chrome/Selenium is not available in the environment
        """
        # Rotate search engines periodically to avoid detection
        self.rotate_search_engine()
        
        # Get current search engine  
        search_engine = self.search_engines[self.current_search_engine]
        base_url = search_engine["url"]
        
        # Craft search query based on what data we have
        if designation and designation.strip():
            # Use company + designation as per client request
            search_query = f"{company} {designation} site:linkedin.com/in/"
            logger.info(f"[Fallback] Searching with company + designation: {company} {designation}")
        else:
            # If no designation, try with name and company
            search_query = f"{name} {company} site:linkedin.com/in/"
            logger.info(f"[Fallback] Searching with name + company: {name} {company}")
        
        encoded_query = urllib.parse.quote(search_query)
        full_url = f"{base_url}{encoded_query}"
        
        logger.info(f"[Fallback] Searching with {search_engine['name']} for: {search_query}")
        
        # Add random delay to mimic human behavior
        time.sleep(random.uniform(2, 5))
        
        try:
            # Use requests to get search results
            response = self.session.get(
                full_url, 
                timeout=10,
                headers={
                    'User-Agent': random.choice([
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
                    ])
                }
            )
            
            if response.status_code != 200:
                logger.warning(f"[Fallback] Search request failed with status code {response.status_code}")
                return None, "Error: Search failed"
            
            # Extract LinkedIn URLs from the HTML content
            html_content = response.text
            linkedin_urls = []
            
            # Simple regex pattern to extract LinkedIn profile URLs
            import re
            linkedin_pattern = r'https?://(?:www\.)?linkedin\.com/in/[a-zA-Z0-9_-]+(?:/[a-zA-Z0-9_%]+)?'
            matches = re.findall(linkedin_pattern, html_content)
            
            for url in matches:
                # Check if URL is not already in our list (remove duplicates)
                clean_url = url.split('?')[0]  # Remove any query parameters
                if clean_url not in linkedin_urls:
                    linkedin_urls.append(clean_url)
                    logger.info(f"[Fallback] Found LinkedIn URL: {clean_url}")
            
            # If we found a LinkedIn profile URL
            if linkedin_urls:
                # Return the first URL found
                best_url = linkedin_urls[0]
                
                # Ensure URL doesn't contain any suspicious patterns (company pages, etc.)
                suspicious_patterns = [
                    "/dir/", "/topic/", "/company/", "/school/", 
                    "/feed/", "/jobs/", "/pulse/", "/groups/"
                ]
                if any(pattern in best_url for pattern in suspicious_patterns):
                    # Try the next URL if available
                    if len(linkedin_urls) > 1:
                        best_url = linkedin_urls[1]
                        if any(pattern in best_url for pattern in suspicious_patterns):
                            return None, "Not Found"
                    else:
                        return None, "Not Found"
                
                # Strip off protocol (https://) to standardize URL format
                if best_url.startswith("https://"):
                    best_url = best_url[8:]  # Remove "https://"
                elif best_url.startswith("http://"):
                    best_url = best_url[7:]  # Remove "http://"
                
                # Success - found a LinkedIn profile URL
                return best_url, "Success"
            
            return None, "Not Found"
            
        except Exception as e:
            logger.error(f"[Fallback] Error during search: {e}")
            return None, f"Error: {str(e)[:100]}"

    def search_linkedin_profile(self, name, company, designation="", retries=0):
        """
        Search for LinkedIn profile using Yahoo and Bing search engines with human-like behavior,
        then open and extract profile information directly from LinkedIn
        """
        # If Selenium is not available or not working, use fallback method
        if not self.selenium_mode:
            logger.info("Using fallback search method (no Selenium)")
            return self.fallback_search_profile(name, company, designation)
        
        if retries >= 3:
            logger.warning(f"Maximum retries reached for {name} at {company}")
            return None, "Error: Max retries exceeded"
        
        try:
            # Rotate search engines periodically to avoid detection
            self.rotate_search_engine()
            
            # Get current search engine
            search_engine = self.search_engines[self.current_search_engine]
            base_url = search_engine["url"]
            
            # Craft search query based on what data we have
            if designation and designation.strip():
                # Use company + designation as per client request
                search_query = f"{company} {designation} site:linkedin.com/in/"
                logger.info(f"Searching with company + designation: {company} {designation}")
            else:
                # If no designation, try with name and company
                search_query = f"{name} {company} site:linkedin.com/in/"
                logger.info(f"Searching with name + company: {name} {company}")
            
            logger.info(f"Searching with {search_engine['name']} for: {search_query}")
            
            # First navigate to search engine homepage to appear more human-like
            self.driver.get(base_url)
            
            # Human-like initial delay before starting search
            time.sleep(random.uniform(1.0, 2.5))
            
            # Find search box based on the search engine
            if search_engine["name"] == "Yahoo":
                search_box = self.driver.find_element(By.NAME, "p")
            else:  # Bing
                search_box = self.driver.find_element(By.NAME, "q")
            
            # Clear any existing text in the search box
            search_box.clear()
            
            # Type the query with random pauses between keystrokes (human-like typing)
            logger.info("Typing search query with human-like behavior...")
            for char in search_query:
                search_box.send_keys(char)
                # Random typing speed variation
                time.sleep(random.uniform(0.05, 0.2))
            
            # Add a slight pause before hitting Enter (human-like)
            time.sleep(random.uniform(0.5, 1.2))
            
            # Press Enter to submit the search
            search_box.send_keys(Keys.RETURN)
            logger.info("Search query submitted")
            
            # Human-like delay after search to let results load
            time.sleep(random.uniform(2.0, 4.0))
            
            # Perform a small human-like scroll down the page
            self.driver.execute_script("window.scrollBy(0, 300);")
            
            # Additional human delay
            self.human_delay()
            
            # Wait for results to load
            WebDriverWait(self.driver, 12).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Extract LinkedIn URLs from search results
            linkedin_urls = []
            
            # Different selectors based on search engine
            if search_engine["name"] == "Bing":
                # Bing search results - improved selector reliability
                logger.info("Processing Bing search results")
                # First try specific selector
                results = self.driver.find_elements(By.CSS_SELECTOR, "li.b_algo")
                
                # If no results with specific selector, try more general ones
                if not results:
                    results = self.driver.find_elements(By.CSS_SELECTOR, ".b_algo")
                
                # If still no results, try even more general approach
                if not results:
                    results = self.driver.find_elements(By.TAG_NAME, "li")
                
                for result in results:
                    try:
                        links = result.find_elements(By.TAG_NAME, "a")
                        for link in links:
                            url = link.get_attribute("href")
                            if url and "linkedin.com/in/" in url:
                                linkedin_urls.append(url)
                                logger.info(f"Found LinkedIn URL in Bing results: {url}")
                    except (NoSuchElementException, StaleElementReferenceException):
                        continue
            
            elif search_engine["name"] == "Yahoo":
                # Yahoo search results - improved selector reliability
                logger.info("Processing Yahoo search results")
                # Try specific Yahoo selectors
                results = self.driver.find_elements(By.CSS_SELECTOR, "div.algo")
                
                # If no results with specific selector, try more general ones
                if not results:
                    results = self.driver.find_elements(By.CSS_SELECTOR, ".algo")
                
                # If still no results, try even more general approach
                if not results:
                    results = self.driver.find_elements(By.CSS_SELECTOR, "div[class*='result']")
                
                for result in results:
                    try:
                        # Try specific selector first
                        links = result.find_elements(By.CSS_SELECTOR, "a.d-ib")
                        
                        # If no links, try generic selector
                        if not links:
                            links = result.find_elements(By.TAG_NAME, "a")
                            
                        for link in links:
                            url = link.get_attribute("href")
                            if url and "linkedin.com/in/" in url:
                                linkedin_urls.append(url)
                                logger.info(f"Found LinkedIn URL in Yahoo results: {url}")
                    except (NoSuchElementException, StaleElementReferenceException):
                        continue
            
            # If search results are not found with specific selectors, try a more generic approach
            if not linkedin_urls:
                # Generic link extraction as fallback (improved)
                logger.info("Using generic link extraction as fallback")
                try:
                    # Wait for all links to be available
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.TAG_NAME, "a"))
                    )
                    
                    # Extract all links on the page
                    all_links = self.driver.find_elements(By.TAG_NAME, "a")
                    logger.info(f"Found {len(all_links)} total links on page")
                    
                    # Debug: log the page source if few links found
                    if len(all_links) < 5:
                        logger.info(f"Page source (first 1000 chars): {self.driver.page_source[:1000]}")
                        
                    for link in all_links:
                        try:
                            url = link.get_attribute("href")
                            if url and "linkedin.com/in/" in url:
                                linkedin_urls.append(url)
                                logger.info(f"Found LinkedIn URL via generic extraction: {url}")
                        except (WebDriverException, StaleElementReferenceException):
                            continue
                except Exception as e:
                    logger.error(f"Error during generic link extraction: {e}")
                    
                # If still no URLs found, try one more time with JavaScript
                if not linkedin_urls:
                    try:
                        # Use JavaScript to find all links - most reliable method
                        links_js = self.driver.execute_script(
                            """
                            const links = Array.from(document.querySelectorAll('a'));
                            return links.map(link => link.href).filter(href => 
                                href && href.includes('linkedin.com/in/')
                            );
                            """
                        )
                        if links_js:
                            linkedin_urls.extend(links_js)
                            logger.info(f"Found {len(links_js)} LinkedIn URLs via JavaScript")
                    except Exception as e:
                        logger.error(f"Error during JavaScript link extraction: {e}")
            
            # If we found a LinkedIn profile URL
            if linkedin_urls:
                # Clean the URL to remove tracking parameters
                best_url = linkedin_urls[0].split("?")[0]
                
                # Ensure URL doesn't contain any suspicious patterns (company pages, etc.)
                suspicious_patterns = [
                    "/dir/", "/topic/", "/company/", "/school/", 
                    "/feed/", "/jobs/", "/pulse/", "/groups/"
                ]
                if any(pattern in best_url for pattern in suspicious_patterns):
                    # Try the next URL if available
                    if len(linkedin_urls) > 1:
                        best_url = linkedin_urls[1].split("?")[0]
                        if any(pattern in best_url for pattern in suspicious_patterns):
                            return None, "Not Found"
                    else:
                        return None, "Not Found"
                
                # Verify it's a valid LinkedIn profile
                if "/in/" in best_url:
                    logger.info(f"Found valid LinkedIn profile: {best_url}")
                    
                    # NEW FEATURE: Actually visit the LinkedIn profile page to extract more info
                    try:
                        logger.info(f"Visiting LinkedIn profile at {best_url}")
                        # Navigate to the LinkedIn profile
                        self.driver.get(best_url)
                        
                        # Add a longer human-like delay when visiting LinkedIn
                        time.sleep(random.uniform(3, 6))
                        
                        # Wait for the profile page to load
                        WebDriverWait(self.driver, 15).until(
                            EC.presence_of_element_located((By.TAG_NAME, "body"))
                        )
                        
                        # Get the current URL (which may be different if redirected)
                        current_url = self.driver.current_url
                        
                        # Clean the URL again
                        if current_url and "linkedin.com/in/" in current_url:
                            best_url = current_url.split("?")[0]
                        
                        logger.info(f"Successfully loaded LinkedIn profile: {best_url}")
                    except Exception as e:
                        logger.warning(f"Error while visiting LinkedIn profile (continuing anyway): {e}")
                    
                    # Strip off protocol (https://) to standardize URL format
                    if best_url.startswith("https://"):
                        best_url = best_url[8:]  # Remove "https://"
                    elif best_url.startswith("http://"):
                        best_url = best_url[7:]  # Remove "http://"
                    
                    # Success - found and visited a LinkedIn profile URL
                    return best_url, "Success"
            
            # No LinkedIn profile found with current search engine
            logger.info(f"No LinkedIn profile found for {company} with {search_engine['name']}")
            
            # Try with the other search engine if we haven't already
            if retries == 0:  
                # Force switch to the other search engine
                self.current_search_engine = (self.current_search_engine + 1) % len(self.search_engines)
                logger.info(f"Switching to {self.search_engines[self.current_search_engine]['name']} for second attempt")
                time.sleep(random.uniform(2, 4))  # Brief delay before trying next engine
                return self.search_linkedin_profile(name, company, designation, retries + 1)
            
            return None, "Not Found"
        
        except TimeoutException:
            logger.warning(f"Timeout searching for {name} at {company}, retrying...")
            # Add a longer delay before retry
            time.sleep(random.uniform(5, 10))
            return self.search_linkedin_profile(name, company, designation, retries + 1)
        
        except WebDriverException as e:
            logger.error(f"WebDriver error: {e}")
            
            # Check if we need to restart the driver
            if "invalid session id" in str(e).lower() or "session not created" in str(e).lower():
                logger.warning("Invalid session, restarting the WebDriver")
                self.close_driver()
                self.setup_driver()
                # Add a longer delay before retry
                time.sleep(random.uniform(7, 15))
            
            # Retry
            return self.search_linkedin_profile(name, company, designation, retries + 1)
        
        except Exception as e:
            logger.error(f"Error searching for LinkedIn profile: {e}")
            return None, f"Error: {str(e)[:100]}"
    
    def close_driver(self):
        """Safely close the browser driver if available"""
        if self.selenium_mode and self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed successfully")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {e}")
    
    def __del__(self):
        """Destructor to ensure driver is closed"""
        self.close_driver()


def process_linkedin_extraction(df, task_id=None, resume_index=0):
    """
    Process LinkedIn data extraction from a pandas DataFrame.
    
    Args:
        df: DataFrame with at least "Person Name" and "Current Company Name" columns
        task_id: Task ID for tracking and checkpointing
        resume_index: Index to resume from if recovering a task
    
    Returns:
        Updated DataFrame with LinkedIn URLs and output path
    """
    from app import processing_tasks, save_checkpoint, CHECKPOINT_DIR, OUTPUT_FOLDER
    
    if task_id is not None and task_id in processing_tasks:
        task_info = processing_tasks[task_id]
    else:
        task_info = {}
    
    # Initialize the scraper
    scraper = LinkedInScraper(headless=True)
    
    # Counters for statistics
    total_rows = len(df)
    start_time = datetime.now()
    success_count = 0
    not_found_count = 0
    error_count = 0
    search_count = 0  # Track number of searches to detect rate limiting
    cool_down_threshold = 30  # Number of searches before potential cooldown
    
    try:
        # Process each row
        for i in range(resume_index, total_rows):
            # Skip already processed rows
            if pd.notna(df.at[i, "LinkedIn URL"]) and df.at[i, "Processing Status"] == "Success":
                success_count += 1
                continue
            elif df.at[i, "Processing Status"] == "Not Found":
                not_found_count += 1
                continue
            
            # Get person, designation and company names
            person_name = df.at[i, "Person Name"]
            company_name = df.at[i, "Current Company Name"]
            
            # Try to get designation if it exists
            designation = ""
            if "Designation" in df.columns:
                designation = df.at[i, "Designation"] if pd.notna(df.at[i, "Designation"]) else ""
            
            # Update task info
            if task_id:
                # Calculate progress percentage
                progress_percentage = int((i / total_rows) * 100)
                
                # Calculate estimated time remaining
                elapsed_time = (datetime.now() - start_time).total_seconds()
                if i > resume_index:
                    avg_time_per_row = elapsed_time / (i - resume_index)
                    remaining_rows = total_rows - i
                    remaining_seconds = avg_time_per_row * remaining_rows
                    remaining_time = timedelta(seconds=remaining_seconds)
                    
                    # Format remaining time
                    hours, remainder = divmod(remaining_seconds, 3600)
                    minutes, seconds = divmod(remainder, 60)
                    remaining_time_formatted = f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
                    
                    # Calculate estimated completion time
                    estimated_completion_time = (datetime.now() + timedelta(seconds=remaining_seconds)).isoformat()
                else:
                    remaining_time_formatted = "Calculating..."
                    estimated_completion_time = None
                
                # Update task information
                processing_tasks[task_id].update({
                    'status': 'processing',
                    'processed_companies': i,
                    'current_company': f"{person_name} at {company_name}",
                    'progress_percentage': progress_percentage,
                    'success_count': success_count,
                    'failure_count': not_found_count + error_count,
                    'remaining_time_formatted': remaining_time_formatted,
                    'estimated_completion_time': estimated_completion_time
                })
                
                # Save checkpoint every 5 records or when progress percentage changes
                if i % 5 == 0 or (i > 0 and progress_percentage != processing_tasks[task_id].get('last_progress', 0)):
                    processing_tasks[task_id]['last_progress'] = progress_percentage
                    
                    # Save current results as a partial file for download
                    partial_output_filename = f"linkedin_partial_{task_id}.csv"
                    partial_output_path = os.path.join(OUTPUT_FOLDER, partial_output_filename)
                    df.to_csv(partial_output_path, index=False)
                    processing_tasks[task_id]['partial_results'] = partial_output_filename
                    
                    # Save checkpoint
                    checkpoint_file = os.path.join(CHECKPOINT_DIR, f'checkpoint_{task_id}.csv')
                    df.to_csv(checkpoint_file, index=False)
                    
                    # Save metadata
                    metadata = {
                        'task_id': task_id,
                        'timestamp': datetime.now().isoformat(),
                        'current_index': i,
                        'total_rows': total_rows,
                        'success_count': success_count,
                        'not_found_count': not_found_count,
                        'error_count': error_count,
                        'search_count': search_count
                    }
                    
                    metadata_file = os.path.join(CHECKPOINT_DIR, f'metadata_{task_id}.json')
                    with open(metadata_file, 'w') as f:
                        json.dump(metadata, f)
            
            # Process the row with improved progress reporting and delays
            # Calculate progress and time estimates similar to the example code
            elapsed_time = (datetime.now() - start_time).total_seconds()
            if i > resume_index:
                avg_time_per_record = elapsed_time / (i - resume_index)
                remaining_records = total_rows - i
                remaining_seconds = avg_time_per_record * remaining_records
                remaining_time = timedelta(seconds=int(remaining_seconds))
                estimated_completion = datetime.now() + remaining_time
                
                time_estimate = (
                    f"~{str(remaining_time).split('.')[0]} remaining | "
                    f"ETA: {estimated_completion.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                time_estimate = "Calculating time remaining..."
            
            progress = f"[{i+1}/{total_rows}]"
            logger.info(f"Processing {progress}: {person_name} at {company_name}")
            logger.info(f"Time estimate: {time_estimate}")
            
            # Add variable human-like delay between profiles (from example code)
            delay = random.uniform(1, 5)  # 1-5 seconds between profiles
            logger.info(f"Waiting {delay:.2f} seconds before processing...")
            time.sleep(delay)
            
            # Periodic longer breaks to avoid detection (from example code)
            if (i + 1) % 20 == 0 and i > 0:
                pause_time = random.uniform(20, 40)
                logger.info(f"Taking a short break for {pause_time:.1f} seconds after {i + 1} profiles...")
                time.sleep(pause_time)
            
            # Check if we need to take a more substantial cooldown break (every N searches)
            search_count += 1
            if search_count >= cool_down_threshold:
                cooldown_time = random.uniform(180, 360)  # 3-6 minutes
                logger.info(f"Taking a preventive cooldown break for {cooldown_time:.0f} seconds after {search_count} searches")
                search_count = 0
                
                if task_id:
                    # Mark as interrupted
                    processing_tasks[task_id].update({
                        'status': 'interrupted',
                        'interrupted_at': datetime.now().isoformat(),
                        'resume_after': cooldown_time,
                        'interrupted_index': i
                    })
                    
                    # Save partial results for download
                    partial_output_filename = f"linkedin_partial_{task_id}.csv"
                    partial_output_path = os.path.join(OUTPUT_FOLDER, partial_output_filename)
                    df.to_csv(partial_output_path, index=False)
                    processing_tasks[task_id]['partial_results'] = partial_output_filename
                    
                    # Return the current state to allow resuming later
                    return df, partial_output_path
                
                # If not tracking with task_id, just sleep for the cooldown period
                time.sleep(cooldown_time)
            
            # Try to find LinkedIn profile
            try:
                linkedin_url, status = scraper.search_linkedin_profile(person_name, company_name, designation)
                
                # Store results in dataframe
                df.at[i, "LinkedIn URL"] = linkedin_url
                df.at[i, "Processing Status"] = status
                
                # Update counters
                if status == "Success":
                    success_count += 1
                elif status == "Not Found":
                    not_found_count += 1
                elif status.startswith("Error"):
                    error_count += 1
                
                # Human-like delay between searches
                time.sleep(random.uniform(3, 7))
                
            except Exception as e:
                logger.error(f"Error processing {person_name} at {company_name}: {e}")
                df.at[i, "Processing Status"] = f"Error: {str(e)[:100]}"
                error_count += 1
        
        # Finalize output file
        output_filename = f"linkedin_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)
        df.to_csv(output_path, index=False)
        
        # Update task info if using task tracking
        if task_id and task_id in processing_tasks:
            processing_tasks[task_id].update({
                'status': 'completed',
                'completed_at': datetime.now().isoformat(),
                'output_filename': output_filename,
                'processed_companies': total_rows,
                'success_count': success_count,
                'failure_count': not_found_count + error_count,
                'progress_percentage': 100
            })
        
        logger.info(f"LinkedIn extraction completed. Total: {total_rows}, Found: {success_count}, Not Found: {not_found_count}, Errors: {error_count}")
        return df, output_path
    
    except Exception as e:
        logger.exception(f"Error in LinkedIn extraction process: {e}")
        
        # Update task info if using task tracking
        if task_id and task_id in processing_tasks:
            processing_tasks[task_id].update({
                'status': 'error',
                'error': str(e),
                'error_traceback': traceback.format_exc()
            })
        
        # Close the scraper
        scraper.close_driver()
        
        # Return the current state of the dataframe
        return df, None
    
    finally:
        # Ensure the scraper is closed
        scraper.close_driver()


def run_linkedin_extraction_in_background(df, task_id):
    """Run the LinkedIn extraction process in a background thread"""
    def background_task():
        try:
            process_linkedin_extraction(df, task_id)
        except Exception as e:
            logger.exception(f"Background task error: {e}")
    
    thread = threading.Thread(target=background_task)
    thread.daemon = True
    thread.start()
    
    return thread