{% extends 'layout.html' %}

{% block title %}Task History | Business Intelligence Tools{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-10">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark d-flex justify-content-between align-items-center">
                    <h2 class="mb-0 text-white">Task History</h2>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-home me-2"></i>Home
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if tasks and tasks|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-dark table-hover task-history-table mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th scope="col" class="border-bottom">Task ID</th>
                                        <th scope="col" class="border-bottom">Type</th>
                                        <th scope="col" class="border-bottom">Filename</th>
                                        <th scope="col" class="border-bottom">Status</th>
                                        <th scope="col" class="border-bottom">Progress</th>
                                        <th scope="col" class="border-bottom">Created</th>
                                        <th scope="col" class="border-bottom">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for task in tasks %}
                                    <tr class="task-row {% if task.status == 'error' %}table-danger{% elif task.status == 'success' %}table-success{% elif task.status == 'processing' %}table-info{% elif task.status == 'interrupted' %}table-warning{% endif %}" 
                                       data-task-id="{{ task.id }}">
                                        <td class="text-nowrap align-middle">
                                            <span class="badge bg-dark text-white border border-secondary">{{ task.id[:8] }}...</span>
                                        </td>
                                        <td class="align-middle">
                                            {% if 'linkedin' in task.details %}
                                                <span class="badge bg-info text-dark">
                                                    <i class="fab fa-linkedin me-1"></i> LinkedIn
                                                </span>
                                            {% else %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-building me-1"></i> CIN
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="align-middle text-truncate" style="max-width: 180px;">
                                            {{ task.filename or 'N/A' }}
                                        </td>
                                        <td class="align-middle">
                                            {% if task.status == 'success' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i> Complete
                                                </span>
                                            {% elif task.status == 'processing' %}
                                                <span class="badge bg-info text-dark">
                                                    <i class="fas fa-spinner fa-spin me-1"></i> Processing
                                                </span>
                                            {% elif task.status == 'queued' %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-clock me-1"></i> Queued
                                                </span>
                                            {% elif task.status == 'error' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i> Error
                                                </span>
                                            {% elif task.status == 'interrupted' %}
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-pause me-1"></i> Interrupted
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    {{ task.status }}
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="align-middle">
                                            <div class="d-flex align-items-center">
                                                {% if task.progress and task.progress > 0 %}
                                                    <div class="progress flex-grow-1" style="height: 10px; background-color: #444;">
                                                        <div class="progress-bar {% if task.status == 'error' %}bg-danger{% elif task.status == 'interrupted' %}bg-warning{% elif task.status == 'success' %}bg-success{% else %}bg-info{% endif %}" 
                                                            role="progressbar" 
                                                            style="width: {{ task.progress }}%;" 
                                                            aria-valuenow="{{ task.progress }}" 
                                                            aria-valuemin="0" 
                                                            aria-valuemax="100">
                                                        </div>
                                                    </div>
                                                    <span class="ms-2 small">{{ task.progress }}%</span>
                                                {% else %}
                                                    <span class="text-muted">Not started</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="align-middle text-nowrap">
                                            <small class="text-muted">{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                        </td>
                                        <td class="align-middle">
                                            <div class="btn-group btn-group-sm">
                                                <!-- For completed tasks -->
                                                {% if task.status == 'success' or task.status == 'completed' %}
                                                    {% if 'linkedin' in task.details %}
                                                        <a href="{{ url_for('download_linkedin_results') }}" class="btn btn-outline-success" title="Download results">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                    {% else %}
                                                        <a href="{{ url_for('download') }}" class="btn btn-outline-success" title="Download results">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                    {% endif %}
                                                
                                                <!-- For processing tasks -->
                                                {% elif task.status == 'processing' or task.status == 'queued' %}
                                                    {% if 'linkedin' in task.details %}
                                                        <a href="{{ url_for('download_linkedin_partial', task_id=task.id) }}" class="btn btn-outline-info" title="Download partial results">
                                                            <i class="fas fa-file-download"></i>
                                                        </a>
                                                        <a href="{{ url_for('linkedin_processing', task_id=task.id) }}" class="btn btn-outline-info" title="View progress">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ url_for('cancel_task', task_id=task.id) }}" class="btn btn-outline-danger" title="Cancel task" onclick="return confirm('Are you sure you want to cancel this task?');">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    {% else %}
                                                        <a href="{{ url_for('download_partial', task_id=task.id) }}" class="btn btn-outline-info" title="Download partial results">
                                                            <i class="fas fa-file-download"></i>
                                                        </a>
                                                        <a href="{{ url_for('processing', task_id=task.id) }}" class="btn btn-outline-info" title="View progress">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ url_for('cancel_task', task_id=task.id) }}" class="btn btn-outline-danger" title="Cancel task" onclick="return confirm('Are you sure you want to cancel this task?');">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    {% endif %}
                                                
                                                <!-- For error or interrupted tasks -->
                                                {% elif task.status in ['error', 'interrupted'] %}
                                                    <!-- Show download option if there's any progress -->
                                                    {% if task.progress and task.progress > 0 %}
                                                        {% if 'linkedin' in task.details %}
                                                            <a href="{{ url_for('download_linkedin_partial', task_id=task.id) }}" class="btn btn-outline-warning" title="Download partial results">
                                                                <i class="fas fa-file-download"></i>
                                                            </a>
                                                        {% else %}
                                                            <a href="{{ url_for('download_partial', task_id=task.id) }}" class="btn btn-outline-warning" title="Download partial results">
                                                                <i class="fas fa-file-download"></i>
                                                            </a>
                                                        {% endif %}
                                                    {% endif %}
                                                    
                                                    <!-- Resume option -->
                                                    {% if 'linkedin' in task.details %}
                                                        <a href="{{ url_for('resume_linkedin_task', task_id=task.id) }}" class="btn btn-outline-primary" title="Resume task">
                                                            <i class="fas fa-play"></i>
                                                        </a>
                                                    {% else %}
                                                        <a href="{{ url_for('resume_task', task_id=task.id) }}" class="btn btn-outline-primary" title="Resume task">
                                                            <i class="fas fa-play"></i>
                                                        </a>
                                                    {% endif %}
                                                
                                                <!-- For any other status, provide download option if partial_results exists -->
                                                {% else %}
                                                    {% if task.details and task.details.get('partial_results') %}
                                                        {% if 'linkedin' in task.details %}
                                                            <a href="{{ url_for('download_linkedin_partial', task_id=task.id) }}" class="btn btn-outline-secondary" title="Download available results">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        {% else %}
                                                            <a href="{{ url_for('download_partial', task_id=task.id) }}" class="btn btn-outline-secondary" title="Download available results">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        {% endif %}
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center p-5">
                            <div class="text-muted mb-4">
                                <i class="fas fa-history fa-4x"></i>
                            </div>
                            <h4 class="text-white">No tasks found</h4>
                            <p class="text-white-50">Your extraction task history will appear here.</p>
                            <div class="mt-4">
                                <a href="{{ url_for('cin_extractor') }}" class="btn btn-primary me-2">
                                    <i class="fas fa-building me-2"></i> Try CIN Extractor
                                </a>
                                <a href="{{ url_for('linkedin_extractor') }}" class="btn btn-info">
                                    <i class="fab fa-linkedin me-2"></i> Try LinkedIn Extractor
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if there are any processing or queued tasks
    const hasProcessingTasks = {{ 'true' if tasks and tasks|selectattr('status', 'in', ['processing', 'queued'])|list|length > 0 else 'false' }};
    
    // If we have active tasks, setup auto-refresh
    if (hasProcessingTasks) {
        // Refresh the page every 10 seconds to show updated progress
        setTimeout(function() {
            window.location.reload();
        }, 10000);
    }
    
    // Add click event to task rows to make them clickable
    const taskRows = document.querySelectorAll('.task-row');
    taskRows.forEach(row => {
        row.addEventListener('click', function(e) {
            // Don't trigger if they clicked on a button
            if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON' || 
                e.target.closest('a') || e.target.closest('button')) {
                return;
            }
            
            const taskId = this.dataset.taskId;
            const status = this.querySelector('td:nth-child(4) .badge').textContent.trim();
            
            // If processing, go to processing page
            if (status.includes('Processing')) {
                if (this.querySelector('td:nth-child(2) .badge').textContent.includes('LinkedIn')) {
                    window.location.href = `/linkedin_processing/${taskId}`;
                } else {
                    window.location.href = `/processing/${taskId}`;
                }
            } 
            // If error or interrupted, go to resume
            else if (status.includes('Error') || status.includes('Interrupted')) {
                if (this.querySelector('td:nth-child(2) .badge').textContent.includes('LinkedIn')) {
                    window.location.href = `/resume_linkedin_task/${taskId}`;
                } else {
                    window.location.href = `/resume_task/${taskId}`;
                }
            }
        });
    });
});
</script>
{% endblock %}