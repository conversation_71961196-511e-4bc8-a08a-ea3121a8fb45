<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIN Extractor - Loading...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .loading-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            color: #e74c3c;
            margin-top: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="spinner"></div>
        <h2>CIN Extractor</h2>
        <p>Loading application...</p>
        <div class="error-message" id="error-message">
            <p>If this page doesn't redirect automatically, <a href="/.netlify/functions/app/">click here</a> to access the application.</p>
        </div>
    </div>

    <script>
        // Redirect to the Flask app after a short delay
        setTimeout(function() {
            window.location.href = '/.netlify/functions/app/';
        }, 2000);

        // Show error message after 10 seconds if redirect doesn't work
        setTimeout(function() {
            document.getElementById('error-message').style.display = 'block';
        }, 10000);
    </script>
</body>
</html>
