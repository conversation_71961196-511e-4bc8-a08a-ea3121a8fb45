/* Custom styles with enhanced mobile and touch support */

/* Base styles */
:root {
    --touch-highlight: rgba(var(--bs-primary-rgb), 0.15);
    --touch-feedback-time: 0.2s;
}

html, body {
    height: 100%;
    overscroll-behavior-y: contain; /* Prevent pull-to-refresh on mobile */
    touch-action: manipulation; /* Improve touch response */
    -webkit-tap-highlight-color: transparent; /* Remove default tap highlight on mobile */
}

body {
    display: flex;
    flex-direction: column;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container sizing */
.container {
    max-width: 1200px;
    width: 100%;
    padding-right: var(--bs-gutter-x, 1rem);
    padding-left: var(--bs-gutter-x, 1rem);
}

/* Style for CIN code */
code {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    word-break: break-all; /* Handle long CIN codes on mobile */
    display: inline-block;
}

/* Make table rows more readable */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.03);
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Status badges styling - larger target for mobile */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.7em;
    border-radius: 0.25rem;
}

/* Card styling with mobile considerations */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1rem;
    overflow: hidden; /* Ensure content doesn't overflow on small screens */
    border-radius: 0.5rem;
}

/* Disable hover effects on touch devices for better performance */
@media (hover: hover) {
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
}

/* Form styling with mobile enhancements */
.form-control, .form-select {
    padding: 0.75rem 1rem; /* Larger touch target */
    margin-bottom: 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Enhanced button styling for touch */
.btn {
    transition: all var(--touch-feedback-time) ease;
    padding: 0.75rem 1.25rem; /* Larger touch target */
    border-radius: 0.5rem;
    font-weight: 500;
}

/* Touch feedback for buttons and interactive elements */
.touch-friendly, .touch-friendly-icon {
    cursor: pointer;
    transition: background-color var(--touch-feedback-time) ease;
    position: relative;
}

.touch-active, .btn:active {
    background-color: var(--touch-highlight) !important;
    transform: scale(0.97);
}

.touch-friendly-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    width: 44px;
    border-radius: 50%;
}

/* Larger navbar toggle for better touch targeting */
.navbar-toggler {
    padding: 0.75rem;
    border-radius: 0.5rem;
}

/* Mobile swipe container */
#mobile-swipe-container {
    flex: 1;
    width: 100%;
    position: relative;
}

/* Responsive table improvements */
.table-responsive {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Custom scrollbar for better touch scrolling */
.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background-color: rgba(var(--bs-primary-rgb), 0.5);
    border-radius: 3px;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
    body {
        font-size: 0.95rem;
    }
    
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th, .table td {
        padding: 0.75rem 0.5rem;
    }
    
    /* Stack buttons on mobile */
    .d-flex .btn {
        margin-bottom: 0.5rem;
    }
    
    /* Fixed position footer on mobile */
    footer {
        margin-top: auto !important;
    }
    
    /* Adjust spacing on mobile */
    .py-5 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }
    
    /* Make sure mobile inputs don't get hidden behind keyboard */
    input[type="file"] {
        font-size: 0.9rem;
    }
}

/* Large screen optimizations */
@media (min-width: 992px) {
    .card {
        border-radius: 0.75rem;
    }
    
    .btn {
        border-radius: 0.5rem;
    }
}

/* Ensure dark mode works well */
[data-bs-theme="dark"] .form-control::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

/* Improved dark theme for alerts */
[data-bs-theme="dark"] .alert {
    background-color: rgba(33, 37, 41, 0.9);
    border-color: rgba(55, 65, 81, 0.5);
    color: #e9ecef;
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(46, 34, 0, 0.8);
    border-color: #664d03;
    color: #e9ecef;
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(15, 41, 15, 0.8);
    border-color: #0f5132;
    color: #e9ecef;
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(58, 18, 18, 0.8);
    border-color: #842029;
    color: #e9ecef;
}

[data-bs-theme="dark"] .alert-info {
    background-color: rgba(12, 44, 55, 0.8);
    border-color: #087990;
    color: #e9ecef;
}

[data-bs-theme="dark"] .alert .text-dark {
    color: #e9ecef !important;
}

/* Enhanced card styling for dark mode */
[data-bs-theme="dark"] .card {
    background-color: #2b3035;
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .card .bg-warning {
    background-color: #664d03 !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .card .text-dark {
    color: #e9ecef !important;
}

/* Active state for navigation */
.nav-item .nav-link.active {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 0.5rem;
}

/* Improve file input appearance */
.form-control[type="file"] {
    padding: 0.5rem;
    cursor: pointer;
}

/* Animation for processing spinner */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.spinner-border {
    animation: spinner-border 1s linear infinite, pulse 2s infinite ease-in-out;
}

/* Prevent scrollbar jumping on page transitions */
html {
    scrollbar-width: thin;
    scrollbar-color: rgba(var(--bs-primary-rgb), 0.5) rgba(255, 255, 255, 0.05);
}
