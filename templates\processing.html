{% extends 'layout.html' %}

{% block title %}CIN Extractor - Processing{% endblock %}

{% block content %}
<div class="container py-4 py-md-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white p-3">
                    <h2 class="text-center mb-0 d-flex align-items-center justify-content-center">
                        <i class="bi bi-gear-wide-connected me-2 processing-icon"></i>
                        <span>Processing Your File</span>
                    </h2>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <div class="spinner-container mb-3">
                            <div class="spinner-border text-info" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h4 class="mb-3" id="status-title">Processing in Background</h4>
                        <p class="text-muted">
                            Your file is being processed. This may take several minutes depending on the size of your file.
                            <br>This page will automatically update when complete.
                        </p>
                    </div>
                    
                    <div class="progress-container mb-4">
                        <div class="progress" style="height: 12px;">
                            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-info" role="progressbar" style="width: 0%"></div>
                        </div>
                        <p class="mt-3 text-center fw-bold" id="status-message">Initializing...</p>
                    </div>
                    
                    <!-- Real-time progress stats -->
                    <div class="progress-stats mb-4 d-none" id="progress-stats">
                        <div class="row text-center">
                            <div class="col-6 col-md-3 mb-3">
                                <div class="card border-primary bg-dark h-100">
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1 text-primary">Companies</h6>
                                        <p class="card-text h5 mb-0 text-white" id="companies-counter">0/0</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3">
                                <div class="card border-info bg-dark h-100">
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1 text-info">Progress</h6>
                                        <p class="card-text h5 mb-0 text-white" id="progress-percentage">0%</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3">
                                <div class="card border-warning bg-dark h-100">
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1 text-warning">Time Left</h6>
                                        <p class="card-text h5 mb-0 text-white" id="time-remaining">--:--</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3">
                                <div class="card border-secondary bg-dark h-100">
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1 text-secondary">Current</h6>
                                        <p class="card-text text-truncate text-white" id="current-company">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Download partial results button - hidden initially, shown after processing some companies -->
                        <div class="download-partial-container mt-3 d-none" id="download-partial-container">
                            <a href="{{ url_for('download_partial', task_id=task_id) }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-file-download me-1"></i> Download Partial Results
                            </a>
                            <div class="small text-muted mt-1">
                                Download the current results while processing continues
                            </div>
                        </div>
                    </div>
                    
                    <!-- Processing time explanation card for mobile -->
                    <div class="card bg-dark mb-4">
                        <div class="card-body">
                            <h5 class="mb-3">
                                <i class="bi bi-clock-history me-2"></i>
                                Processing Information
                            </h5>
                            <ul class="mb-0 ps-3">
                                <li class="mb-2">The system is searching for CIN numbers for each company</li>
                                <li class="mb-2">Human-like delays (3-20 seconds) are added between searches</li>
                                <li class="mb-2">Search engine rotation occurs every 50 companies</li>
                                <li>Large files may take significant time to process</li>
                            </ul>
                            <div class="mt-3 text-center">
                                <div class="badge bg-secondary py-2">
                                    <span>Created by Prashant</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Success alert (hidden by default) -->
                    <div id="completed-alert" class="alert alert-success d-none">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle-fill fs-1 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Processing Complete!</h5>
                                <p class="mb-3">Your results are ready to view.</p>
                                <a href="{{ url_for('results') }}" class="btn btn-success touch-friendly">
                                    <i class="bi bi-table me-1"></i> View Results
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Interruption alert (hidden by default) -->
                    <div id="interrupted-alert" class="alert alert-warning d-none">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-pause-circle-fill fs-1 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Processing Paused</h5>
                                <p class="mb-2" id="interruption-message">
                                    Processing was interrupted due to potential bot detection.
                                    <span class="d-block mt-1">To prevent getting blocked, we need to wait <span id="countdown-timer" class="fw-bold">5:15</span> before resuming.</span>
                                </p>
                                <div class="d-flex flex-column flex-md-row gap-2 mt-3">
                                    <a id="download-partial-btn" href="#" class="btn btn-info touch-friendly">
                                        <i class="bi bi-download me-1"></i> Download Current Results
                                    </a>
                                    <button id="resume-now-btn" class="btn btn-success touch-friendly d-none">
                                        <i class="bi bi-play-fill me-1"></i> Resume Processing
                                    </button>
                                </div>
                                <div class="progress mt-3" style="height: 10px;">
                                    <div id="cooldown-progress" class="progress-bar bg-warning" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Error alert (hidden by default) -->
                    <div id="error-alert" class="alert alert-danger d-none">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-exclamation-triangle-fill fs-1 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Processing Error</h5>
                                <p class="mb-2" id="error-message">An error occurred during processing.</p>
                                
                                <div id="error-recovery-options" class="mt-4">
                                    <div class="d-flex flex-column flex-md-row gap-2">
                                        <!-- Download recovery file button -->
                                        <a id="download-recovery-btn" href="{{ url_for('download') }}" class="btn btn-success touch-friendly">
                                            <i class="bi bi-download me-1"></i> Download Recovered Data
                                        </a>
                                        
                                        <!-- Start over button -->
                                        <a href="{{ url_for('index') }}" class="btn btn-danger touch-friendly">
                                            <i class="bi bi-arrow-counterclockwise me-1"></i> Start Over
                                        </a>
                                    </div>
                                    <div class="mt-2 text-success">
                                        <small>
                                            <i class="bi bi-info-circle me-1"></i>
                                            Don't worry! Your data has been automatically saved up to the point of error.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Touch-friendly cancel button -->
                    <div class="text-center mt-4">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary touch-friendly">
                            <i class="bi bi-x-circle me-1"></i> Cancel and Return to Upload
                        </a>
                    </div>
                </div>
                <div class="card-footer text-center text-muted p-3">
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="spinner-grow spinner-grow-sm text-info me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <small>Task ID: {{ task_id }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Animated progress elements
    const progressBar = document.getElementById('progress-bar');
    const statusMessage = document.getElementById('status-message');
    const statusTitle = document.getElementById('status-title');
    
    // Function to format time remaining
    function formatTimeRemaining(seconds) {
        if (!seconds || seconds <= 0) return '--:--';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            return `${remainingSeconds}s`;
        }
    }
    
    // Function to format countdown time
    function formatCountdownTime(seconds) {
        if (!seconds || seconds <= 0) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    // Function to check task status
    function checkTaskStatus() {
        fetch('/task_status/{{ task_id }}')
            .then(response => response.json())
            .then(data => {
                // Update progress bar and messages based on status
                switch(data.status) {
                    case 'queued':
                        progressBar.style.width = '10%';
                        statusMessage.innerText = 'Waiting in queue - Processing will start soon...';
                        statusTitle.innerText = 'In Queue';
                        
                        // Hide progress stats if visible
                        document.getElementById('progress-stats').classList.add('d-none');
                        
                        // Hide interruption alert if visible
                        document.getElementById('interrupted-alert').classList.add('d-none');
                        break;
                        
                    case 'interrupted':
                        // Clear any animations
                        if (window.dotsInterval) clearInterval(window.dotsInterval);
                        if (window.pulseInterval) clearInterval(window.pulseInterval);
                        
                        // Show interrupted state
                        progressBar.style.width = data.progress_percentage ? `${data.progress_percentage}%` : '50%';
                        progressBar.classList.remove('progress-bar-animated');
                        progressBar.classList.add('bg-warning');
                        statusMessage.innerText = 'Processing paused - waiting to resume';
                        statusTitle.innerText = 'Processing Paused';
                        
                        // Hide progress stats
                        document.getElementById('progress-stats').classList.add('d-none');
                        
                        // Show and update interruption alert
                        const interruptedAlert = document.getElementById('interrupted-alert');
                        interruptedAlert.classList.remove('d-none');
                        
                        // Update download partial button
                        const downloadPartialBtn = document.getElementById('download-partial-btn');
                        if (data.partial_results) {
                            downloadPartialBtn.href = `/download_partial/${data.task_id}`;
                        } else {
                            downloadPartialBtn.classList.add('disabled');
                            downloadPartialBtn.setAttribute('aria-disabled', 'true');
                        }
                        
                        // Update cooldown timer if available
                        if (data.cooldown_remaining !== undefined) {
                            // Update countdown timer
                            const countdownTimer = document.getElementById('countdown-timer');
                            countdownTimer.innerText = formatCountdownTime(data.cooldown_remaining);
                            
                            // Update progress bar
                            const cooldownProgress = document.getElementById('cooldown-progress');
                            const totalCooldown = data.resume_after || 315;
                            const progress = 100 - ((data.cooldown_remaining / totalCooldown) * 100);
                            cooldownProgress.style.width = `${progress}%`;
                            
                            // Show resume button if cooldown is complete
                            const resumeNowBtn = document.getElementById('resume-now-btn');
                            if (data.can_resume) {
                                resumeNowBtn.classList.remove('d-none');
                                countdownTimer.innerText = 'Ready!';
                                
                                // Setup resume button
                                resumeNowBtn.onclick = function() {
                                    window.location.href = `/resume_task/${data.task_id}`;
                                };
                            }
                        }
                        
                        // Add haptic feedback for interruption (different pattern)
                        if (window.navigator && window.navigator.vibrate && !window.interruptionNotified) {
                            window.navigator.vibrate([100, 100, 100]);
                            window.interruptionNotified = true;
                        }
                        break;
                        
                    case 'processing':
                        // Show progress stats
                        document.getElementById('progress-stats').classList.remove('d-none');
                        
                        // Hide interruption alert if visible
                        document.getElementById('interrupted-alert').classList.add('d-none');
                        
                        // Reset interruption notification flag
                        window.interruptionNotified = false;
                        
                        // Update progress details if available
                        if (data.progress_percentage !== undefined) {
                            progressBar.style.width = `${data.progress_percentage}%`;
                            updateElementWithPulse('progress-percentage', `${data.progress_percentage}%`);
                        } else {
                            // Use pulsing effect if no precise progress yet
                            if (!window.pulseInterval) {
                                startPulseAnimation();
                            }
                        }
                        
                        // Update companies counter
                        if (data.processed_companies !== undefined && data.total_companies !== undefined) {
                            updateElementWithPulse('companies-counter', `${data.processed_companies}/${data.total_companies}`);
                            
                            // Show download partial button if we have processed at least one company
                            // and have partial results available
                            if (data.processed_companies > 0) {
                                // Check if we have partial results available
                                if (data.partial_results) {
                                    // Show download partial container
                                    const downloadContainer = document.getElementById('download-partial-container');
                                    if (downloadContainer) {
                                        downloadContainer.classList.remove('d-none');
                                    }
                                }
                            }
                        }
                        
                        // Update time remaining
                        if (data.remaining_seconds !== undefined) {
                            updateElementWithPulse('time-remaining', formatTimeRemaining(data.remaining_seconds));
                        } else if (data.estimated_total_seconds !== undefined) {
                            updateElementWithPulse('time-remaining', formatTimeRemaining(data.estimated_total_seconds));
                        }
                        
                        // Update current company
                        if (data.current_company) {
                            const element = document.getElementById('current-company');
                            if (element && element.innerText !== data.current_company) {
                                element.innerText = data.current_company;
                                element.classList.add('pulse');
                                setTimeout(() => element.classList.remove('pulse'), 600);
                            }
                        }
                        
                        // Update status message with more detail
                        let statusMessageText = 'Processing in progress';
                        if (data.processed_companies !== undefined && data.total_companies !== undefined) {
                            const percentage = Math.round((data.processed_companies / data.total_companies) * 100);
                            statusMessageText = `Processing: ${percentage}% complete`;
                            if (data.remaining_time_formatted) {
                                statusMessageText += ` • ${data.remaining_time_formatted} remaining`;
                            }
                        }
                        statusMessage.innerText = statusMessageText;
                        
                        statusTitle.innerText = 'Processing Companies';
                        
                        // Add animated dots to status message for visual feedback
                        if (!window.dotsInterval) {
                            window.dotsInterval = setInterval(() => {
                                if (!statusMessage.innerText.endsWith('...')) {
                                    statusMessage.innerText += '.';
                                } else {
                                    statusMessage.innerText = statusMessage.innerText.slice(0, -3);
                                }
                            }, 500);
                        }
                        break;
                        
                    case 'completed':
                        // Clear any animations
                        if (window.dotsInterval) clearInterval(window.dotsInterval);
                        if (window.pulseInterval) clearInterval(window.pulseInterval);
                        
                        // Show completion state
                        progressBar.style.width = '100%';
                        progressBar.classList.remove('progress-bar-animated');
                        progressBar.classList.add('bg-success');
                        statusMessage.innerText = 'Processing complete!';
                        statusTitle.innerText = 'Complete!';
                        
                        // Show success message
                        document.getElementById('completed-alert').classList.remove('d-none');
                        
                        // Add haptic feedback if available
                        if (window.navigator && window.navigator.vibrate) {
                            window.navigator.vibrate([100, 50, 100]);
                        }
                        
                        // Stop polling
                        clearInterval(statusChecker);
                        
                        // Redirect after 3 seconds
                        setTimeout(() => {
                            window.location.href = "{{ url_for('results') }}";
                        }, 3000);
                        break;
                    case 'error':
                        // Clear any animations
                        if (window.dotsInterval) clearInterval(window.dotsInterval);
                        if (window.pulseInterval) clearInterval(window.pulseInterval);
                        
                        // Show error state
                        progressBar.style.width = '100%';
                        progressBar.classList.remove('progress-bar-animated');
                        progressBar.classList.add('bg-danger');
                        statusMessage.innerText = 'Error occurred, but your data is safe';
                        statusTitle.innerText = 'Error Recovery';
                        
                        // Show and populate error alert
                        document.getElementById('error-alert').classList.remove('d-none');
                        document.getElementById('error-message').innerText = data.error || 'Unknown error occurred during processing';
                        
                        // Configure the recovery download button
                        const downloadRecoveryBtn = document.getElementById('download-recovery-btn');
                        if (data.error_recovery_file) {
                            // We have a specific error recovery file (latest checkpoint)
                            downloadRecoveryBtn.href = "{{ url_for('download') }}";
                            downloadRecoveryBtn.classList.add('pulse-success');
                            downloadRecoveryBtn.innerHTML = '<i class="bi bi-download me-1"></i> Download Recovered Data <span class="badge bg-success ms-2">Auto-Saved</span>';
                        } else if (data.partial_results) {
                            // We have partial results from regular processing
                            downloadRecoveryBtn.href = `/download_partial/${data.task_id}`;
                            downloadRecoveryBtn.classList.add('pulse-success');
                        } else if (data.last_checkpoint_file) {
                            // We have a checkpoint file reference
                            downloadRecoveryBtn.href = "{{ url_for('download') }}";
                            downloadRecoveryBtn.classList.add('pulse-success');
                        } else {
                            // No recovery data available
                            downloadRecoveryBtn.classList.add('disabled');
                            downloadRecoveryBtn.setAttribute('aria-disabled', 'true');
                            downloadRecoveryBtn.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i> No Recovery Data Available';
                        }
                        
                        // Add haptic feedback if available (error pattern)
                        if (window.navigator && window.navigator.vibrate) {
                            window.navigator.vibrate([100, 50, 100, 50, 300]);
                        }
                        
                        // Stop polling
                        clearInterval(statusChecker);
                        break;
                    default:
                        statusMessage.innerText = `Unknown status: ${data.status}`;
                }
            })
            .catch(error => {
                console.error('Error checking task status:', error);
                // Show network error
                document.getElementById('error-alert').classList.remove('d-none');
                document.getElementById('error-message').innerText = 'Error communicating with the server. Please check your connection.';
            });
    }

    // Create pulsing effect for better visual feedback during waiting
    let pulseDirection = 1;
    let pulseValue = 50;
    
    function startPulseAnimation() {
        // Clear any existing pulse intervals
        if (window.pulseInterval) {
            clearInterval(window.pulseInterval);
        }
        
        // Reset pulse values
        pulseDirection = 1;
        pulseValue = 50;
        
        // Start new pulse animation
        window.pulseInterval = setInterval(() => {
            if (progressBar) {
                pulseValue += (pulseDirection * 3);
                if (pulseValue >= 60) {
                    pulseDirection = -1;
                } else if (pulseValue <= 40) {
                    pulseDirection = 1;
                }
                progressBar.style.width = `${pulseValue}%`;
            }
        }, 800);
    }
    
    // Start pulse animation immediately
    startPulseAnimation();
    
    // Check status every 3 seconds
    const statusChecker = setInterval(checkTaskStatus, 3000);
    
    // Check immediately on page load
    document.addEventListener('DOMContentLoaded', function() {
        checkTaskStatus();
        
        // Add animation to the processing icon
        const processingIcon = document.querySelector('.processing-icon');
        if (processingIcon) {
            setInterval(() => {
                processingIcon.classList.toggle('rotate-icon');
            }, 2000);
        }
        
        // Enable swipe to cancel
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            const container = document.querySelector('.card');
            if (container) {
                const hammer = new Hammer(container);
                hammer.get('swipe').set({ direction: Hammer.DIRECTION_DOWN });
                hammer.on('swipedown', function() {
                    if (confirm('Cancel processing and return to upload?')) {
                        window.location.href = "{{ url_for('index') }}";
                    }
                });
            }
        }
    });
    
    // Function to update element with pulse animation
    function updateElementWithPulse(elementId, newValue) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        // Skip if value hasn't changed
        if (element.innerText === newValue) return;
        
        // Update the value
        element.innerText = newValue;
        
        // Add pulse animation
        element.classList.add('pulse');
        
        // Remove pulse class after animation completes
        setTimeout(() => {
            element.classList.remove('pulse');
        }, 600);
    }
    
    // Add touch-friendly styling
    document.querySelectorAll('.touch-friendly').forEach(el => {
        el.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        }, { passive: true });
        
        el.addEventListener('touchend', function() {
            this.classList.remove('touch-active');
        }, { passive: true });
    });
</script>

<style>
    /* Animated processing icon */
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .rotate-icon {
        animation: rotate 1.5s ease-in-out;
    }
    
    /* Pulsating effect for spinner */
    .spinner-container {
        animation: pulse 2s infinite ease-in-out;
    }
    
    @keyframes pulse {
        0% { opacity: 0.8; transform: scale(0.97); }
        50% { opacity: 1; transform: scale(1.03); }
        100% { opacity: 0.8; transform: scale(0.97); }
    }
    
    /* Progress stats cards styling */
    .progress-stats .card {
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }
    
    .progress-stats .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
    
    .progress-stats .card-body {
        padding: 0.75rem;
    }
    
    /* Make progress bar more visible */
    .progress {
        height: 16px !important;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.2);
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }
    
    .progress-bar {
        border-radius: 8px;
        background-image: linear-gradient(to right, var(--bs-info), var(--bs-primary));
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
    
    /* Truncate company names */
    #current-company {
        font-size: 0.85rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }
    
    /* Touch-friendly cards with feedback */
    .progress-stats .card.bg-primary { background-color: var(--bs-primary) !important; }
    .progress-stats .card.bg-info { background-color: var(--bs-info) !important; }
    .progress-stats .card.bg-warning { background-color: var(--bs-warning) !important; }
    .progress-stats .card.bg-secondary { background-color: var(--bs-secondary) !important; }
    
    /* Add animation for metrics that update */
    .card-text.h5 {
        transition: all 0.3s ease;
    }
    
    .card-text.h5.pulse {
        animation: text-pulse 0.6s ease-in-out;
    }
    
    @keyframes text-pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    /* Success pulse animation for download recovery button */
    .pulse-success {
        animation: success-pulse 2s infinite;
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    
    @keyframes success-pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }
</style>
{% endblock %}