import requests
import re
import logging
import random
import time
from bs4 import BeautifulSoup
from urllib.parse import quote_plus

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Constants for search engine rotation
SEARCH_ENGINE_YAHOO = 'yahoo'
SEARCH_ENGINE_BING = 'bing'
SEARCH_ENGINE_ROTATION_COUNT = 50  # Switch search engine after every 50 searches

# Constants for extraction types
EXTRACT_CIN = 'cin'
EXTRACT_TURNOVER = 'turnover'
EXTRACT_BOTH = 'both'

# User agents to rotate (more varied and realistic)
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
    'Mozilla/5.0 (iPad; CPU OS 17_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1'
]

# Simulate human behavior with realistic but shorter delays to prevent timeouts
def human_delay():
    """Simulates human browsing behavior with shorter delays for background processing"""
    # Shorter delay between 1-5 seconds for background processing
    # This helps prevent worker timeouts while still mimicking human behavior
    base_delay = random.uniform(1, 5)
    
    # Shorter micro-delays
    micro_delays = [random.uniform(0.05, 0.2) for _ in range(random.randint(1, 3))]
    
    # Execute the delays in sequence to mimic human interactions
    time.sleep(base_delay)
    for delay in micro_delays:
        time.sleep(delay)
    
    logger.debug(f"Applied human-like delay of approximately {base_delay + sum(micro_delays):.2f} seconds")

def search_yahoo_for_cin(company_name):
    """
    Search Yahoo for a company's CIN number and extract it from the search results
    with human-like behavior
    """
    # Format the search query
    query = f"{company_name} CIN number ZaubaCorp"
    encoded_query = quote_plus(query)
    
    # Yahoo search URL
    url = f"https://search.yahoo.com/search?p={encoded_query}"
    
    # Set headers with random user agent and more realistic browser fingerprint
    headers = {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.yahoo.com/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    try:
        logger.debug(f"Starting human-like search for {company_name}")
        
        # Simulate human thinking with shorter delay to prevent timeouts
        time.sleep(random.uniform(0.5, 1.5))
        
        # Send request to Yahoo
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Simulate reading the search results
        human_delay()
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Search for CIN pattern in the search results
        # CIN is typically 21 characters: L74999TG1991PLC013391 (one letter followed by 5 digits, then 2 letters, 4 digits, 3 letters and 6 digits)
        cin_pattern = r'[A-Z][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}'
        
        # First check for direct rich results or specific sections
        # These are Yahoo-specific selector patterns that might contain CIN information
        selectors = [
            'div.compText', 'span.fz-ms', 'div.dd algo', 'div.compTitle',
            'div.compCardList', 'div.compList', 'div.dd.togr', 'li.first'
        ]
        
        # Context patterns that often come before CIN numbers
        cin_contexts = [
            'CIN:', 'CIN :', 'CIN -', 'CIN -:', 'CIN Number:', 'CIN No:',
            'Corporate Identity Number:', 'Corporate Identification Number:',
            'CIN Number -', 'CIN No. -'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                
                # Try to find CIN with context
                for context in cin_contexts:
                    if context in text:
                        # Look for the CIN pattern after the context string
                        context_idx = text.find(context) + len(context)
                        remaining_text = text[context_idx:context_idx + 100].strip()
                        cin_match = re.search(cin_pattern, remaining_text)
                        if cin_match:
                            logger.debug(f"Found CIN for {company_name} with context: {context}")
                            return cin_match.group(0)
                
                # If not found with context, try direct pattern match
                cin_match = re.search(cin_pattern, text)
                if cin_match:
                    logger.debug(f"Found CIN for {company_name} with direct pattern")
                    return cin_match.group(0)
        
        # Search in all text content if not found in specific sections
        page_text = soup.get_text()
        
        # Look for CIN with context in the entire page
        for context in cin_contexts:
            if context in page_text:
                context_idx = page_text.find(context) + len(context)
                remaining_text = page_text[context_idx:context_idx + 100].strip()
                cin_match = re.search(cin_pattern, remaining_text)
                if cin_match:
                    logger.debug(f"Found CIN for {company_name} in full page with context: {context}")
                    return cin_match.group(0)
        
        # Direct pattern match as last resort
        cin_match = re.search(cin_pattern, page_text)
        if cin_match:
            logger.debug(f"Found CIN for {company_name} in full page with direct pattern")
            return cin_match.group(0)
        
        # Try an alternative approach - sometimes CIN is directly shown in search results
        # Look for text that contains "CIN" followed by the pattern
        cin_sections = re.finditer(r'CIN\s*[:.\-]?\s*([A-Z0-9]{21})', page_text, re.IGNORECASE)
        for section in cin_sections:
            if len(section.group(1)) == 21:  # CIN is always 21 characters
                logger.debug(f"Found CIN for {company_name} using alternative method")
                return section.group(1)
        
        # If no match found
        logger.debug(f"No CIN found for {company_name}")
        return None
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error for {company_name}: {e}")
        raise Exception(f"Error accessing Yahoo: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error for {company_name}")
        raise Exception(f"Unexpected error: {str(e)}")

def search_bing_for_cin(company_name):
    """
    Search Bing for a company's CIN number and extract it from the search results
    with human-like behavior
    """
    # Format the search query
    query = f"{company_name} CIN number ZaubaCorp"
    encoded_query = quote_plus(query)
    
    # Bing search URL
    url = f"https://www.bing.com/search?q={encoded_query}"
    
    # Set headers with random user agent and more realistic browser fingerprint
    headers = {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.bing.com/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    try:
        logger.debug(f"Starting human-like Bing search for {company_name}")
        
        # Simulate human thinking with shorter delay to prevent timeouts
        time.sleep(random.uniform(0.5, 1.5))
        
        # Send request to Bing
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Simulate reading the search results
        human_delay()
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Search for CIN pattern in the search results
        # CIN is typically 21 characters: L74999TG1991PLC013391 (one letter followed by 5 digits, then 2 letters, 4 digits, 3 letters and 6 digits)
        cin_pattern = r'[A-Z][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}'
        
        # First check for direct rich results or specific sections
        # These are Bing-specific selector patterns that might contain CIN information
        selectors = [
            'div.b_caption', 'p.b_paractl', 'div.b_factrow', 'div.b_subModule',
            'li.b_algo', 'div.b_title', 'div.b_snippet', 'span.b_infoboxAd'
        ]
        
        # Context patterns that often come before CIN numbers (same as Yahoo)
        cin_contexts = [
            'CIN:', 'CIN :', 'CIN -', 'CIN -:', 'CIN Number:', 'CIN No:',
            'Corporate Identity Number:', 'Corporate Identification Number:',
            'CIN Number -', 'CIN No. -'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                
                # Try to find CIN with context
                for context in cin_contexts:
                    if context in text:
                        # Look for the CIN pattern after the context string
                        context_idx = text.find(context) + len(context)
                        remaining_text = text[context_idx:context_idx + 100].strip()
                        cin_match = re.search(cin_pattern, remaining_text)
                        if cin_match:
                            logger.debug(f"Found CIN for {company_name} with context: {context} (Bing)")
                            return cin_match.group(0)
                
                # If not found with context, try direct pattern match
                cin_match = re.search(cin_pattern, text)
                if cin_match:
                    logger.debug(f"Found CIN for {company_name} with direct pattern (Bing)")
                    return cin_match.group(0)
        
        # Search in all text content if not found in specific sections
        page_text = soup.get_text()
        
        # Look for CIN with context in the entire page
        for context in cin_contexts:
            if context in page_text:
                context_idx = page_text.find(context) + len(context)
                remaining_text = page_text[context_idx:context_idx + 100].strip()
                cin_match = re.search(cin_pattern, remaining_text)
                if cin_match:
                    logger.debug(f"Found CIN for {company_name} in full page with context: {context} (Bing)")
                    return cin_match.group(0)
        
        # Direct pattern match as last resort
        cin_match = re.search(cin_pattern, page_text)
        if cin_match:
            logger.debug(f"Found CIN for {company_name} in full page with direct pattern (Bing)")
            return cin_match.group(0)
        
        # Try an alternative approach - sometimes CIN is directly shown in search results
        # Look for text that contains "CIN" followed by the pattern
        cin_sections = re.finditer(r'CIN\s*[:.\-]?\s*([A-Z0-9]{21})', page_text, re.IGNORECASE)
        for section in cin_sections:
            if len(section.group(1)) == 21:  # CIN is always 21 characters
                logger.debug(f"Found CIN for {company_name} using alternative method (Bing)")
                return section.group(1)
        
        # If no match found
        logger.debug(f"No CIN found for {company_name} on Bing")
        return None
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error for {company_name} on Bing: {e}")
        raise Exception(f"Error accessing Bing: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error for {company_name} on Bing")
        raise Exception(f"Unexpected error on Bing: {str(e)}")

def search_for_cin(company_name, search_count=0):
    """
    Search for a company's CIN number using search engine rotation strategy
    
    Args:
        company_name: Name of the company to search for
        search_count: Current search count (used for rotation)
        
    Returns:
        CIN number or None if not found
    """
    # Determine which search engine to use based on count
    # Rotate between Yahoo and Bing for every SEARCH_ENGINE_ROTATION_COUNT searches
    search_engine = SEARCH_ENGINE_YAHOO if (search_count // SEARCH_ENGINE_ROTATION_COUNT) % 2 == 0 else SEARCH_ENGINE_BING
    
    logger.debug(f"Using search engine: {search_engine} (search count: {search_count})")
    
    # Try primary search engine
    try:
        if search_engine == SEARCH_ENGINE_YAHOO:
            return search_yahoo_for_cin(company_name)
        else:
            return search_bing_for_cin(company_name)
    except Exception as e:
        logger.warning(f"Error with primary search engine {search_engine}: {str(e)}")
        
        # If primary search engine fails, try the alternative one
        try:
            if search_engine == SEARCH_ENGINE_YAHOO:
                logger.info(f"Trying Bing as fallback for {company_name}")
                return search_bing_for_cin(company_name)
            else:
                logger.info(f"Trying Yahoo as fallback for {company_name}")
                return search_yahoo_for_cin(company_name)
        except Exception as fallback_error:
            logger.error(f"Fallback search engine also failed: {str(fallback_error)}")
            raise Exception(f"Both search engines failed: {str(e)} and fallback: {str(fallback_error)}")

def search_yahoo_for_turnover(company_name):
    """
    Search Yahoo for a company's turnover data and extract it from the search results
    with human-like behavior
    """
    # Format the search query
    query = f"{company_name} turnover tofler"
    encoded_query = quote_plus(query)
    
    # Yahoo search URL
    url = f"https://search.yahoo.com/search?p={encoded_query}"
    
    # Set headers with random user agent and more realistic browser fingerprint
    headers = {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.yahoo.com/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    try:
        logger.debug(f"Starting human-like turnover search for {company_name}")
        
        # Simulate human thinking with shorter delay to prevent timeouts
        time.sleep(random.uniform(0.5, 1.5))
        
        # Send request to Yahoo
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Simulate reading the search results
        human_delay()
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # First check for direct rich results or specific sections
        # These are Yahoo-specific selector patterns that might contain turnover information
        selectors = [
            'div.compText', 'span.fz-ms', 'div.dd algo', 'div.compTitle',
            'div.compCardList', 'div.compList', 'div.dd.togr', 'li.first'
        ]
        
        # Context patterns that often come before turnover numbers
        turnover_contexts = [
            'revenue range is', 'operating revenue range is', 'revenue range', 
            'turnover is', 'turnover range', 'annual turnover', 'annual revenue'
        ]
        
        # Pattern to match turnover values (e.g., "INR 500+" or "500 Cr+" or similar formats)
        turnover_pattern = r'(?:INR|Rs\.?|₹)?\s*(\d+(?:\.\d+)?)\s*(?:Cr\.?|Crore|Lakh|L|M|Million|Billion|B)?\+?'
        
        # Search in specific sections first
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                
                # Try to find turnover with context
                for context in turnover_contexts:
                    if context.lower() in text.lower():
                        # Look for the turnover pattern after the context string
                        context_idx = text.lower().find(context.lower()) + len(context)
                        remaining_text = text[context_idx:context_idx + 200].strip()
                        
                        # Check if "INR 500+" format is present
                        inr_match = re.search(r'INR\s+(\d+)\+', remaining_text)
                        if inr_match:
                            value = inr_match.group(1)
                            logger.debug(f"Found turnover for {company_name} with INR format: {value}+")
                            return f"{value}+"
                        
                        # Try to match other turnover formats
                        turnover_match = re.search(turnover_pattern, remaining_text)
                        if turnover_match:
                            # Extract full match to preserve formatting
                            full_match = turnover_match.group(0)
                            logger.debug(f"Found turnover for {company_name} with context: {context}")
                            return full_match
        
        # Search in all text content if not found in specific sections
        page_text = soup.get_text()
        
        # Look for turnover with context in the entire page
        for context in turnover_contexts:
            if context.lower() in page_text.lower():
                context_idx = page_text.lower().find(context.lower()) + len(context)
                remaining_text = page_text[context_idx:context_idx + 200].strip()
                
                # Check if "INR 500+" format is present
                inr_match = re.search(r'INR\s+(\d+)\+', remaining_text)
                if inr_match:
                    value = inr_match.group(1)
                    logger.debug(f"Found turnover for {company_name} in full page with INR format: {value}+")
                    return f"{value}+"
                
                # Try to match other turnover formats
                turnover_match = re.search(turnover_pattern, remaining_text)
                if turnover_match:
                    # Extract full match to preserve formatting
                    full_match = turnover_match.group(0)
                    logger.debug(f"Found turnover for {company_name} in full page with context: {context}")
                    return full_match
        
        # If no match found
        logger.debug(f"No turnover data found for {company_name}")
        return None
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error for {company_name} turnover search: {e}")
        raise Exception(f"Error accessing Yahoo for turnover: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error for {company_name} turnover search")
        raise Exception(f"Unexpected error in turnover search: {str(e)}")

def search_bing_for_turnover(company_name):
    """
    Search Bing for a company's turnover data and extract it from the search results
    with human-like behavior
    """
    # Format the search query
    query = f"{company_name} turnover tofler"
    encoded_query = quote_plus(query)
    
    # Bing search URL
    url = f"https://www.bing.com/search?q={encoded_query}"
    
    # Set headers with random user agent and more realistic browser fingerprint
    headers = {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.bing.com/',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-site',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    try:
        logger.debug(f"Starting human-like Bing turnover search for {company_name}")
        
        # Simulate human thinking with shorter delay to prevent timeouts
        time.sleep(random.uniform(0.5, 1.5))
        
        # Send request to Bing
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Simulate reading the search results
        human_delay()
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # First check for direct rich results or specific sections
        # These are Bing-specific selector patterns that might contain turnover information
        selectors = [
            'div.b_caption', 'p.b_paractl', 'div.b_factrow', 'div.b_subModule',
            'li.b_algo', 'div.b_title', 'div.b_snippet', 'span.b_infoboxAd'
        ]
        
        # Context patterns that often come before turnover numbers (same as Yahoo)
        turnover_contexts = [
            'revenue range is', 'operating revenue range is', 'revenue range', 
            'turnover is', 'turnover range', 'annual turnover', 'annual revenue'
        ]
        
        # Pattern to match turnover values (e.g., "INR 500+" or "500 Cr+" or similar formats)
        turnover_pattern = r'(?:INR|Rs\.?|₹)?\s*(\d+(?:\.\d+)?)\s*(?:Cr\.?|Crore|Lakh|L|M|Million|Billion|B)?\+?'
        
        # Search in specific sections first
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                
                # Try to find turnover with context
                for context in turnover_contexts:
                    if context.lower() in text.lower():
                        # Look for the turnover pattern after the context string
                        context_idx = text.lower().find(context.lower()) + len(context)
                        remaining_text = text[context_idx:context_idx + 200].strip()
                        
                        # Check if "INR 500+" format is present
                        inr_match = re.search(r'INR\s+(\d+)\+', remaining_text)
                        if inr_match:
                            value = inr_match.group(1)
                            logger.debug(f"Found turnover for {company_name} with INR format: {value}+ (Bing)")
                            return f"{value}+"
                        
                        # Try to match other turnover formats
                        turnover_match = re.search(turnover_pattern, remaining_text)
                        if turnover_match:
                            # Extract full match to preserve formatting
                            full_match = turnover_match.group(0)
                            logger.debug(f"Found turnover for {company_name} with context: {context} (Bing)")
                            return full_match
        
        # Search in all text content if not found in specific sections
        page_text = soup.get_text()
        
        # Look for turnover with context in the entire page
        for context in turnover_contexts:
            if context.lower() in page_text.lower():
                context_idx = page_text.lower().find(context.lower()) + len(context)
                remaining_text = page_text[context_idx:context_idx + 200].strip()
                
                # Check if "INR 500+" format is present
                inr_match = re.search(r'INR\s+(\d+)\+', remaining_text)
                if inr_match:
                    value = inr_match.group(1)
                    logger.debug(f"Found turnover for {company_name} in full page with INR format: {value}+ (Bing)")
                    return f"{value}+"
                
                # Try to match other turnover formats
                turnover_match = re.search(turnover_pattern, remaining_text)
                if turnover_match:
                    # Extract full match to preserve formatting
                    full_match = turnover_match.group(0)
                    logger.debug(f"Found turnover for {company_name} in full page with context: {context} (Bing)")
                    return full_match
        
        # If no match found
        logger.debug(f"No turnover data found for {company_name} on Bing")
        return None
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error for {company_name} turnover search on Bing: {e}")
        raise Exception(f"Error accessing Bing for turnover: {str(e)}")
    except Exception as e:
        logger.exception(f"Unexpected error for {company_name} turnover search on Bing")
        raise Exception(f"Unexpected error in turnover search on Bing: {str(e)}")

def search_for_turnover(company_name, search_count=0):
    """
    Search for a company's turnover data using search engine rotation strategy
    
    Args:
        company_name: Name of the company to search for
        search_count: Current search count (used for rotation)
        
    Returns:
        Turnover data (e.g., "500+") or None if not found
    """
    # Determine which search engine to use based on count
    # Rotate between Yahoo and Bing for every SEARCH_ENGINE_ROTATION_COUNT searches
    search_engine = SEARCH_ENGINE_YAHOO if (search_count // SEARCH_ENGINE_ROTATION_COUNT) % 2 == 0 else SEARCH_ENGINE_BING
    
    logger.debug(f"Using search engine for turnover: {search_engine} (search count: {search_count})")
    
    # Try primary search engine
    try:
        if search_engine == SEARCH_ENGINE_YAHOO:
            return search_yahoo_for_turnover(company_name)
        else:
            return search_bing_for_turnover(company_name)
    except Exception as e:
        logger.warning(f"Error with primary search engine {search_engine} for turnover: {str(e)}")
        
        # If primary search engine fails, try the alternative one
        try:
            if search_engine == SEARCH_ENGINE_YAHOO:
                logger.info(f"Trying Bing as fallback for {company_name} turnover")
                return search_bing_for_turnover(company_name)
            else:
                logger.info(f"Trying Yahoo as fallback for {company_name} turnover")
                return search_yahoo_for_turnover(company_name)
        except Exception as fallback_error:
            logger.error(f"Fallback search engine also failed for turnover: {str(fallback_error)}")
            raise Exception(f"Both search engines failed for turnover: {str(e)} and fallback: {str(fallback_error)}")

def search_google_for_cin(company_name):
    """
    Legacy function - redirects to search_for_cin for backward compatibility
    """
    logger.warning("Using legacy Google search function - redirecting to search engine rotation")
    return search_for_cin(company_name, 0)
