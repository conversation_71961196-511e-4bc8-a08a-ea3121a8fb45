# CIN Extractor - Netlify Deployment Guide

## Overview
This guide explains how to deploy the CIN Extractor Flask application to Netlify using serverless functions.

## Files Created for Netlify Deployment

### 1. `netlify.toml` - Main configuration file
- Configures build settings
- Sets up redirects to serverless functions
- Specifies Python runtime

### 2. `_redirects` - Backup redirect configuration
- Fallback redirect rules
- Routes all requests to the Flask app

### 3. `netlify/functions/app.py` - Serverless function wrapper
- Wraps the Flask app for Netlify Functions
- Handles HTTP requests and responses
- Includes error handling

### 4. `app_simple.py` - Simplified Flask app
- Lightweight version without heavy dependencies
- Removes Selenium, database operations, and file processing
- Focuses on core UI functionality

### 5. `runtime.txt` - Python version specification
- Specifies Python 3.11 for deployment

### 6. `static/index.html` - Static landing page
- Provides immediate response while app loads
- Redirects to the Flask application

## Deployment Steps

### 1. Connect to Netlify
1. Go to [Netlify](https://netlify.com)
2. Sign up or log in
3. Click "New site from Git"
4. Connect your GitHub repository

### 2. Configure Build Settings
- Build command: `pip install -r requirements.txt`
- Publish directory: `static`
- Functions directory: `netlify/functions`

### 3. Environment Variables (if needed)
Set these in Netlify dashboard under Site settings > Environment variables:
- `PYTHON_VERSION=3.11`

## Current Limitations

Due to Netlify Functions constraints, the following features are temporarily disabled:
- File upload and processing
- Web scraping with Selenium
- Database operations
- Background task processing

## Next Steps for Full Functionality

To restore full functionality, consider:

1. **Use Netlify Blobs for file storage**
2. **Implement client-side file processing**
3. **Use external APIs for web scraping**
4. **Add database integration with Netlify Edge Functions**

## Testing Locally

To test the simplified app locally:
```bash
python app_simple.py
```

## Troubleshooting

### Common Issues:
1. **Import errors**: Check that all dependencies are in requirements.txt
2. **Function timeout**: Netlify Functions have a 10-second timeout
3. **Memory limits**: Functions have memory constraints

### Logs:
Check Netlify function logs in the dashboard under Functions tab.

## Support

If you encounter issues:
1. Check the Netlify deploy logs
2. Review function logs in the Netlify dashboard
3. Test the simplified app locally first
