import os
import logging
import pandas as pd
import csv
import time
import random
import re
import threading
import queue
import math
import json
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, session, send_file, jsonify
from werkzeug.utils import secure_filename
from utils.scraper import search_for_cin, search_for_turnover, human_delay, SEARCH_ENGINE_ROTATION_COUNT, EXTRACT_CIN, EXTRACT_TURNOVER, EXTRACT_BOTH
from models import db, Company

# Global variables for background processing
processing_queue = queue.Queue()
processing_tasks = {}
global_search_count = 0  # Global counter for search engine rotation

# Checkpoint directory for saving recovery data
CHECKPOINT_DIR = 'checkpoints'
if not os.path.exists(CHECKPOINT_DIR):
    os.makedirs(CHECKPOINT_DIR)

# Function to process background tasks
def background_worker():
    print("🔄 BACKGROUND WORKER THREAD INITIALIZED AND RUNNING")
    logger.warning("🔄 BACKGROUND WORKER THREAD INITIALIZED AND RUNNING")

    while True:
        try:
            print("⏳ Background worker waiting for tasks...")
            logger.debug("Background worker waiting for tasks...")

            # Unpack the task from the queue (supports both normal and resume tasks)
            task = processing_queue.get()
            print(f"📋 Background worker got task: {task}")
            logger.warning(f"📋 Background worker processing task: {task}")

            # Handle different task formats (with or without resume info)
            if len(task) == 2:  # Normal task (task_id, filepath)
                task_id, filepath = task
                resume_from_task_id = None
                resume_index = None
            elif len(task) == 3:  # Resume task (task_id, filepath, resume_index)
                task_id, filepath, resume_index = task
                resume_from_task_id = task_id
            else:
                print(f"Invalid task format: {task}")  # Use print for immediate output
                processing_queue.task_done()
                continue

            print(f"Starting background processing for task {task_id}")  # Debug print

            # Update status to processing
            if task_id in processing_tasks:
                processing_tasks[task_id]['status'] = 'processing'
                print(f"Updated task {task_id} status to processing")  # Debug print
            
            # Process the CSV file (with optional resume parameters)
            output_filename = process_csv_in_background(
                filepath, 
                resume_from_task_id=resume_from_task_id, 
                resume_index=resume_index
            )
            
            # Update task status
            if task_id in processing_tasks:
                processing_tasks[task_id]['status'] = 'completed'
                processing_tasks[task_id]['output_filename'] = output_filename
                processing_tasks[task_id]['completion_time'] = datetime.now().isoformat()
                
            logger.debug(f"Completed task {task_id}")
        except Exception as e:
            logger.exception(f"Error in background processing for task {task_id}")
            if task_id in processing_tasks:
                # Save a reference to the last checkpoint file
                last_checkpoint_file = processing_tasks[task_id].get('last_checkpoint_file')
                
                # Create error-specific output filename with timestamp
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                error_output_filename = f'error_recovery_companies_with_cin_{timestamp}.csv'
                
                # Mark the task as error but preserve the data we have so far
                processing_tasks[task_id]['status'] = 'error'
                processing_tasks[task_id]['error'] = str(e)
                processing_tasks[task_id]['error_output_filename'] = error_output_filename
                
                # Create an auto-download file with the processed results up to the point of error
                try:
                    # If we have a checkpoint file, use that data
                    if last_checkpoint_file and os.path.exists(last_checkpoint_file):
                        logger.info(f"Creating error recovery file from checkpoint: {last_checkpoint_file}")
                        
                        # Read the checkpoint data
                        checkpoint_df = pd.read_csv(last_checkpoint_file)
                        
                        # Deduplicate the data
                        checkpoint_df = checkpoint_df.drop_duplicates(subset=['company_name'], keep='first')
                        
                        # Save it as a downloadable error recovery file
                        error_output_path = os.path.join(app.config['OUTPUT_FOLDER'], error_output_filename)
                        checkpoint_df.to_csv(error_output_path, index=False)
                        
                        # Update the task info with the recovery file
                        processing_tasks[task_id]['error_recovery_file'] = error_output_filename
                        
                        logger.info(f"Created error recovery file with {len(checkpoint_df)} companies: {error_output_filename}")
                    
                except Exception as recovery_error:
                    logger.exception(f"Failed to create error recovery file: {recovery_error}")
        finally:
            processing_queue.task_done()

# Configure logging first
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Use a hard-coded secret key to ensure it's always set
app.secret_key = "cin-extractor-fixed-secret-key-20250401"
logger.info("Using hard-coded secret key for Flask sessions")

# Database configuration
database_url = os.environ.get('DATABASE_URL')
if not database_url:
    logger.warning("DATABASE_URL not found, falling back to SQLite")
    database_url = 'sqlite:///companies.db'
else:
    logger.info("Using PostgreSQL database")
    
app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
# Add connection pool settings
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_recycle': 280,  # Recycle connections before PostgreSQL's 5-minute timeout
    'pool_timeout': 20,   # Wait up to 20 seconds for a connection
    'pool_pre_ping': True, # Verify connections before using them (prevents stale connections)
    'max_overflow': 10    # Allow up to 10 overflow connections
}
db.init_app(app)

# Create database tables
with app.app_context():
    # Check if we need to create or update the tables
    try:
        # First check if the Company table has the turnover column
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        has_turnover_column = False
        has_turnover_status_column = False
        has_cin_status_column = False
        has_extraction_type_column = False
        
        for column in inspector.get_columns('company'):
            if column['name'] == 'turnover':
                has_turnover_column = True
            elif column['name'] == 'turnover_status':
                has_turnover_status_column = True
            elif column['name'] == 'cin_status':
                has_cin_status_column = True
            elif column['name'] == 'extraction_type':
                has_extraction_type_column = True
        
        # If the table exists but doesn't have the new columns, add them
        if not has_turnover_column or not has_turnover_status_column or not has_cin_status_column or not has_extraction_type_column:
            # Get the existing tables
            existing_tables = inspector.get_table_names()
            
            if 'company' in existing_tables:
                logger.info("Upgrading existing database schema to add new columns...")
                conn = db.engine.connect()
                
                # Add the turnover column if it doesn't exist
                if not has_turnover_column:
                    conn.execute(db.text('ALTER TABLE company ADD COLUMN turnover VARCHAR(50)'))
                    logger.info("Added turnover column to company table")
                
                # Add the turnover_status column if it doesn't exist
                if not has_turnover_status_column:
                    conn.execute(db.text('ALTER TABLE company ADD COLUMN turnover_status VARCHAR(50)'))
                    logger.info("Added turnover_status column to company table")
                
                # Add the cin_status column if it doesn't exist
                if not has_cin_status_column:
                    conn.execute(db.text('ALTER TABLE company ADD COLUMN cin_status VARCHAR(50)'))
                    logger.info("Added cin_status column to company table")
                
                # Add the extraction_type column if it doesn't exist
                if not has_extraction_type_column:
                    conn.execute(db.text('ALTER TABLE company ADD COLUMN extraction_type VARCHAR(10)'))
                    logger.info("Added extraction_type column to company table")
                
                conn.commit()
                conn.close()
                
                logger.info("Database schema upgraded successfully")
    except Exception as e:
        logger.exception(f"Error while upgrading database schema: {e}")
        # Continue anyway to create_all which will ensure schema is correct
    
    # Now create all tables (this will only create tables/columns that don't exist)
    db.create_all()

# Configure upload folder
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'csv'}

# Create necessary directories
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('home.html')

@app.route('/history')
def task_history():
    """Show history of all extraction tasks with ability to resume from interruptions/errors"""
    # Get all tasks from the task dictionary, sorted by timestamp (newest first)
    tasks = []
    current_time = datetime.now()
    
    for task_id, task_info in processing_tasks.items():
        # Create a new task object with consistent structure
        task = {
            'id': task_id,
            'filename': task_info.get('filename', 'Unknown'),
            'status': task_info.get('status', 'unknown'),
            'progress': task_info.get('progress', 0),
            'details': task_info.get('details', {}),
            'created_at': current_time,  # Default value
            'error': task_info.get('error', None)
        }
        
        # Try to parse the start_time to a datetime object
        if 'start_time' in task_info:
            try:
                start_time = task_info['start_time']
                if isinstance(start_time, str):
                    # Handle ISO format with timezone
                    if start_time.endswith('Z'):
                        start_time = start_time.replace('Z', '+00:00')
                    task['created_at'] = datetime.fromisoformat(start_time)
                elif isinstance(start_time, datetime):
                    task['created_at'] = start_time
            except Exception as e:
                logger.error(f"Error parsing start_time for task {task_id}: {e}")
        
        # Check for interrupted status - if task has an error but progress > 0,
        # or if it's been in processing state for too long (over 1 hour)
        if task['status'] == 'error' and task['progress'] > 0:
            task['status'] = 'interrupted'  # Mark as interrupted instead of error if we have partial results
        elif task['status'] == 'processing':
            # Check if processing has been going on for more than 1 hour without updates
            if task_info.get('last_updated'):
                try:
                    last_updated = task_info['last_updated']
                    if isinstance(last_updated, str):
                        if last_updated.endswith('Z'):
                            last_updated = last_updated.replace('Z', '+00:00')
                        last_updated_time = datetime.fromisoformat(last_updated)
                    elif isinstance(last_updated, datetime):
                        last_updated_time = last_updated
                    
                    # If it's been over 1 hour, mark as interrupted
                    if (current_time - last_updated_time).total_seconds() > 3600:  # 1 hour
                        task['status'] = 'interrupted'
                except Exception as e:
                    logger.error(f"Error checking last_updated time for task {task_id}: {e}")
        
        tasks.append(task)
    
    # Sort tasks by created_at (newest first)
    tasks.sort(key=lambda x: x['created_at'], reverse=True)
    
    # Render the history template with the task data
    return render_template('history.html', tasks=tasks)

@app.route('/health')
def health_check():
    """Health check endpoint to verify background worker status"""
    try:
        # Check if background thread is alive
        thread_alive = background_thread.is_alive() if 'background_thread' in globals() else False

        # Check queue size
        queue_size = processing_queue.qsize()

        # Check active tasks
        active_tasks = len([t for t in processing_tasks.values() if t['status'] in ['queued', 'processing']])

        return jsonify({
            'status': 'healthy',
            'background_worker_alive': thread_alive,
            'queue_size': queue_size,
            'active_tasks': active_tasks,
            'total_tasks': len(processing_tasks)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/debug/queue-test')
def queue_test():
    """Debug endpoint to test if the queue system is working"""
    try:
        # Create a simple test task
        test_task_id = f"test_{int(time.time())}"

        # Add a test entry to processing_tasks
        processing_tasks[test_task_id] = {
            'status': 'test',
            'timestamp': datetime.now().isoformat(),
            'test': True
        }

        return jsonify({
            'message': 'Test task created',
            'task_id': test_task_id,
            'queue_size_before': processing_queue.qsize(),
            'background_worker_alive': background_thread.is_alive() if 'background_thread' in globals() else False
        })
    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/cin-extractor')
def cin_extractor():
    return render_template('index.html')

@app.route('/linkedin-extractor')
def linkedin_extractor():
    return render_template('linkedin_extractor.html')

@app.route('/upload-linkedin', methods=['POST'])
def upload_linkedin_file():
    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        try:
            # Read the CSV file to check size
            try:
                df = pd.read_csv(filepath)
            except Exception:
                # Try again with different encoding
                df = pd.read_csv(filepath, encoding='latin1')
            
            # Remove duplicate rows before processing
            original_count = len(df)
            
            # Check for primary columns to deduplicate on
            deduplicate_columns = []
            for column in ["Person Name", "LinkedIn URL", "Email", "Profile URL"]:
                if column in df.columns:
                    deduplicate_columns.append(column)
            
            # If we have columns to deduplicate on, do it
            if deduplicate_columns:
                df = df.drop_duplicates(subset=deduplicate_columns, keep='first')
                duplicate_count = original_count - len(df)
                if duplicate_count > 0:
                    logger.info(f"Removed {duplicate_count} duplicate entries from LinkedIn input file")
                    flash(f"Removed {duplicate_count} duplicate entries from your input file.", 'info')
            else:
                # If no suitable columns found, warn the user but proceed
                logger.warning("No suitable columns found for deduplication in LinkedIn file")
            
            # Ensure needed columns exist
            required_cols = ["Person Name", "Current Company Name"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                if len(df.columns) >= len(missing_cols):
                    # Use first columns as fallback
                    for i, col in enumerate(missing_cols):
                        if i < len(df.columns):
                            df.rename(columns={df.columns[i]: col}, inplace=True)
                            logger.info(f"Using column '{df.columns[i]}' as '{col}'")
                else:
                    flash(f"CSV file is missing required columns: {', '.join(missing_cols)}", 'danger')
                    return redirect(url_for('linkedin_extractor'))
            
            # Add needed columns if not present
            if "LinkedIn URL" not in df.columns:
                df["LinkedIn URL"] = None
            if "Processing Status" not in df.columns:
                df["Processing Status"] = "Pending"
            
            # Save the modified DataFrame
            df.to_csv(filepath, index=False)
            
            # For larger files, queue for background processing
            task_id = str(int(time.time()))
            
            # Calculate estimated time
            total_rows = len(df)
            avg_time_per_row = 15  # seconds per profile (conservative estimate)
            estimated_total_seconds = total_rows * avg_time_per_row
            
            # Calculate estimated completion time
            start_time = datetime.now()
            estimated_completion_time = start_time + timedelta(seconds=estimated_total_seconds)
            
            # Create a task for tracking
            processing_tasks[task_id] = {
                'status': 'queued',
                'filepath': filepath,
                'timestamp': start_time.strftime("%Y-%m-%d %H:%M:%S"),
                'total_companies': total_rows,
                'processed_companies': 0,
                'current_company': '',
                'start_time': start_time.isoformat(),
                'estimated_completion_time': estimated_completion_time.isoformat(),
                'estimated_total_seconds': estimated_total_seconds,
                'avg_time_per_company': avg_time_per_row,
                'success_count': 0,
                'failure_count': 0,
                'progress_percentage': 0,
                'task_type': 'linkedin'  # Mark this as a LinkedIn task
            }
            
            # Start processing in background
            from utils.linkedin_scraper import run_linkedin_extraction_in_background
            run_linkedin_extraction_in_background(df, task_id)
            
            # Store task ID in session
            session['linkedin_task_id'] = task_id
            
            # Redirect to processing page
            return redirect(url_for('linkedin_processing', task_id=task_id))
        
        except Exception as e:
            logger.exception("Error processing LinkedIn file")
            flash(f'Error processing file: {str(e)}', 'danger')
            return redirect(url_for('linkedin_extractor'))
    else:
        flash('File type not allowed. Please upload a CSV file.', 'danger')
        return redirect(url_for('linkedin_extractor'))

@app.route('/linkedin-processing/<task_id>')
def linkedin_processing(task_id):
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    task_info = processing_tasks[task_id]
    
    # Check if this is a LinkedIn task
    if task_info.get('task_type') != 'linkedin':
        flash('Invalid task type', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # Check for partial results
    task_has_partial_results = 'partial_results' in task_info
    interrupted = task_info['status'] == 'interrupted'
    
    return render_template(
        'linkedin_processing.html', 
        task_id=task_id, 
        interrupted=interrupted,
        task_has_partial_results=task_has_partial_results
    )

@app.route('/linkedin-task-status/<task_id>')
def linkedin_task_status(task_id):
    if task_id not in processing_tasks:
        return jsonify({'status': 'not_found'})
    
    task_info = processing_tasks[task_id].copy()
    
    # If task is completed, update the output filename if available
    if task_info['status'] == 'completed' and 'output_filename' in task_info:
        session['linkedin_output_filename'] = task_info['output_filename']
    
    # Check if interrupted task is ready to be resumed
    if task_info['status'] == 'interrupted':
        interrupted_at = task_info.get('interrupted_at')
        resume_after = task_info.get('resume_after', 315)  # Default to 315 seconds (5.25 min)
        
        if interrupted_at:
            interrupted_time = datetime.fromisoformat(interrupted_at)
            now = datetime.now()
            elapsed_seconds = (now - interrupted_time).total_seconds()
            
            # Calculate remaining time until auto-resume
            cooldown_remaining = max(0, resume_after - elapsed_seconds)
            task_info['cooldown_remaining'] = int(cooldown_remaining)
            task_info['auto_resume_in'] = str(timedelta(seconds=int(cooldown_remaining)))
            
            # Check if it's time to auto-resume
            if elapsed_seconds >= resume_after:
                task_info['can_resume'] = True
    
    return jsonify(task_info)

@app.route('/resume-linkedin-task/<task_id>')
def resume_linkedin_task(task_id):
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    task_info = processing_tasks[task_id]
    
    # Check if this is a LinkedIn task
    if task_info.get('task_type') != 'linkedin':
        flash('Invalid task type', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    if task_info['status'] != 'interrupted':
        flash('Task is not in interrupted state', 'danger')
        return redirect(url_for('linkedin_results'))
    
    # Get the filepath
    filepath = task_info.get('filepath')
    
    if not filepath or not os.path.exists(filepath):
        flash('Original file not found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # Re-start the LinkedIn extraction
    try:
        # Read the CSV file
        df = pd.read_csv(filepath)
        
        # Update task status
        task_info['status'] = 'processing'
        task_info['resume_timestamp'] = datetime.now().isoformat()
        
        # Start processing in background
        from utils.linkedin_scraper import run_linkedin_extraction_in_background
        run_linkedin_extraction_in_background(df, task_id)
        
        flash('LinkedIn extraction resumed from checkpoint', 'success')
    except Exception as e:
        logger.exception(f"Error resuming LinkedIn task: {e}")
        flash(f'Error resuming task: {str(e)}', 'danger')
    
    return redirect(url_for('linkedin_processing', task_id=task_id))

@app.route('/download-linkedin-partial/<task_id>')
def download_linkedin_partial(task_id):
    """Download partial LinkedIn results from any task status"""
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    task_info = processing_tasks[task_id]
    
    # Check if this is a LinkedIn task
    if task_info.get('task_type') != 'linkedin':
        flash('Invalid task type', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # For completed tasks, use their output directly if available
    if task_info['status'] in ['completed', 'success'] and task_info.get('output_filename'):
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], task_info['output_filename'])
        if os.path.exists(output_path):
            # Generate a meaningful filename for the downloaded file
            download_name = f"linkedin_results_{task_id}.csv"
            logger.info(f"Downloading completed LinkedIn results for task {task_id}")
            return send_file(output_path, as_attachment=True, download_name=download_name)
    
    # Check for partial results first
    partial_results = task_info.get('partial_results')
    
    # If no partial results but we have processed some data, try to generate them
    if not partial_results and task_info.get('processed_entries', 0) > 0:
        try:
            # If we have a checkpoint file, use that to generate partial results
            if task_info.get('last_checkpoint_file') and os.path.exists(task_info['last_checkpoint_file']):
                checkpoint_file = task_info['last_checkpoint_file']
                timestamp = int(time.time())
                partial_filename = f"linkedin_partial_{task_id}_{timestamp}.csv"
                partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                
                # Copy the checkpoint file to the output folder
                import shutil
                shutil.copy2(checkpoint_file, partial_filepath)
                
                # Update task info with partial results filename
                partial_results = partial_filename
                processing_tasks[task_id]['partial_results'] = partial_filename
                logger.info(f"Created partial LinkedIn results from checkpoint for task {task_id}: {partial_filename}")
            
            # If we have a processed dataframe, save it as partial results
            elif task_info.get('processed_df') is not None:
                timestamp = int(time.time())
                partial_filename = f"linkedin_partial_{task_id}_{timestamp}.csv"
                partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                
                # Save the processed dataframe to a CSV file
                task_info['processed_df'].to_csv(partial_filepath, index=False)
                
                # Update task info with partial results filename
                partial_results = partial_filename
                processing_tasks[task_id]['partial_results'] = partial_filename
                logger.info(f"Created partial LinkedIn results from processed dataframe for task {task_id}: {partial_filename}")
        except Exception as e:
            logger.error(f"Error generating partial LinkedIn results for task {task_id}: {e}")
    
    # If we have partial results, send them
    if partial_results:
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], partial_results)
        
        # Generate a meaningful filename for the downloaded file
        processed_count = task_info.get('processed_entries', 0)
        total_entries = task_info.get('total_entries', 0)
        if total_entries > 0:
            download_name = f"linkedin_results_partial_{processed_count}_of_{total_entries}.csv"
        else:
            download_name = f"linkedin_results_partial_{task_id}.csv"
        
        logger.info(f"Downloading partial LinkedIn results for task {task_id}: {partial_results}")
        return send_file(output_path, as_attachment=True, download_name=download_name)
    
    # If no results available, check if we can return the original file
    if task_info.get('input_file') and os.path.exists(task_info['input_file']):
        filepath = task_info['input_file']
        download_name = f"original_linkedin_file_{task_id}.csv"
        logger.info(f"No processed LinkedIn data yet, downloading original file for task {task_id}")
        return send_file(filepath, as_attachment=True, download_name=download_name)
    
    # If we can't provide any results
    flash('No LinkedIn results available yet', 'warning')
    return redirect(url_for('linkedin_processing', task_id=task_id))

@app.route('/linkedin-results')
def linkedin_results():
    # Check if we have a task_id in the session
    task_id = session.get('linkedin_task_id')
    
    # If there's a task_id and it's still processing, show the processing status
    if task_id and task_id in processing_tasks:
        task_info = processing_tasks[task_id]
        
        # Check if this is a LinkedIn task
        if task_info.get('task_type') == 'linkedin':
            # Handle different task statuses
            if task_info['status'] in ['queued', 'processing']:
                return redirect(url_for('linkedin_processing', task_id=task_id))
            elif task_info['status'] == 'interrupted':
                # If task was interrupted, show the processing page with interruption info
                return redirect(url_for('linkedin_processing', task_id=task_id))
            elif task_info['status'] == 'completed' and 'output_filename' in task_info:
                # Update the output filename for completed tasks
                session['linkedin_output_filename'] = task_info['output_filename']
    
    # Get the output filename from the session
    output_filename = session.get('linkedin_output_filename')
    if not output_filename:
        flash('No processed file found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # Read the output file to display on the page
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    try:
        df = pd.read_csv(output_path)
        results = df.to_dict('records')
        
        # Calculate statistics
        stats = {
            'total': len(df),
            'success': sum(1 for row in results if row.get('Processing Status') == 'Success'),
            'not_found': sum(1 for row in results if row.get('Processing Status') == 'Not Found'),
            'error': sum(1 for row in results if isinstance(row.get('Processing Status'), str) and row.get('Processing Status').startswith('Error'))
        }
        
        return render_template('linkedin_results.html', results=results, stats=stats, filename=output_filename)
    except Exception as e:
        logger.exception("Error reading LinkedIn results file")
        flash(f'Error reading results: {str(e)}', 'danger')
        return redirect(url_for('linkedin_extractor'))

@app.route('/download-linkedin-results')
def download_linkedin_results():
    output_filename = session.get('linkedin_output_filename')
    
    # If no output filename in session, check if we have a task_id
    if not output_filename:
        task_id = session.get('linkedin_task_id')
        if task_id and task_id in processing_tasks:
            task_info = processing_tasks[task_id]
            
            # Try to find a suitable file to download
            if 'output_filename' in task_info:
                output_filename = task_info['output_filename']
                session['linkedin_output_filename'] = output_filename
            elif 'partial_results' in task_info:
                output_filename = task_info['partial_results']
                session['linkedin_output_filename'] = output_filename
            else:
                # Try to find the most recent partial results file
                try:
                    output_files = sorted([f for f in os.listdir(app.config['OUTPUT_FOLDER']) if f.startswith('linkedin_partial_')], 
                                      key=lambda x: os.path.getmtime(os.path.join(app.config['OUTPUT_FOLDER'], x)), 
                                      reverse=True)
                    
                    if output_files:
                        output_filename = output_files[0]
                        session['linkedin_output_filename'] = output_filename
                except Exception as e:
                    logger.error(f"Error finding most recent LinkedIn results file: {e}")
    
    # If we still don't have an output filename, show error
    if not output_filename:
        flash('No processed LinkedIn file found', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    # Get the path to the output file
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    
    # Check if the file exists
    if not os.path.exists(output_path):
        flash(f'LinkedIn results file not found. Please run a new extraction.', 'danger')
        return redirect(url_for('linkedin_extractor'))
    
    try:
        # Read the file and ensure it has no duplicates
        df = pd.read_csv(output_path)
        
        # Generate a meaningful download filename
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # If we have an 'email' or 'profile_url' column, deduplicate on that
        deduplicate_column = None
        for col in ['email', 'profile_url', 'name', 'company']:
            if col in df.columns:
                deduplicate_column = col
                break
        
        # Remove duplicates if we found a suitable column
        if deduplicate_column:
            original_count = len(df)
            df_deduplicated = df.drop_duplicates(subset=[deduplicate_column], keep='first')
            duplicate_count = original_count - len(df_deduplicated)
            
            # If we found and removed duplicates, create a new deduplicated file
            if duplicate_count > 0:
                logger.info(f"Removed {duplicate_count} duplicate LinkedIn entries from {output_filename}")
                
                # Create a new file for the deduplicated data with a timestamp
                clean_filename = f"linkedin_data_deduplicated_{timestamp}.csv"
                clean_file_path = os.path.join(app.config['OUTPUT_FOLDER'], clean_filename)
                
                # Save the deduplicated data to the new file
                df_deduplicated.to_csv(clean_file_path, index=False)
                
                # Use the deduplicated file for download
                output_path = clean_file_path
                download_name = f"linkedin_data_deduplicated_{timestamp}.csv"
                
                # Update session with new deduplicated file
                session['linkedin_output_filename'] = clean_filename
            else:
                download_name = f"linkedin_data_{timestamp}.csv"
        else:
            # If there's no suitable column to deduplicate on, just use the original file
            download_name = f"linkedin_data_{timestamp}.csv"
        
        # Special case for partial results
        if "partial" in output_filename:
            download_name = f"linkedin_data_partial_{timestamp}.csv"
        
        # Add task progress info if available
        task_id = session.get('linkedin_task_id')
        if task_id and task_id in processing_tasks:
            task_info = processing_tasks[task_id]
            
            if task_info.get('status') == 'processing':
                # For in-progress tasks, add more detail to the filename
                processed = task_info.get('processed_entries', 0)
                total = task_info.get('total_entries', 0)
                
                if total > 0:
                    percent = int((processed / total) * 100)
                    download_name = f"linkedin_data_partial_{processed}_of_{total}_{percent}percent_{timestamp}.csv"
        
        return send_file(output_path, as_attachment=True, download_name=download_name)
    except Exception as e:
        logger.exception(f"Error processing LinkedIn download file {output_filename}")
        flash(f'Error preparing LinkedIn download: {str(e)}', 'danger')
        return redirect(url_for('linkedin_extractor'))

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('cin_extractor'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('cin_extractor'))
    
    # Get the extraction type (cin, turnover, or both)
    extraction_type = request.form.get('extraction_type', 'cin')
    if extraction_type not in ['cin', 'turnover', 'both']:
        extraction_type = 'cin'  # Default to CIN if invalid value
    
    logger.info(f"Extraction type selected: {extraction_type}")
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        try:
            # Read the CSV file to check format
            try:
                df = pd.read_csv(filepath)
            except Exception:
                # Try again with different encoding
                df = pd.read_csv(filepath, encoding='latin1')
            
            # Check if 'company_name' column exists, if not use the first column
            if 'company_name' not in df.columns:
                logger.debug(f"No 'company_name' column found. Available columns: {df.columns.tolist()}")
                if len(df.columns) > 0:
                    first_column = df.columns[0]
                    logger.debug(f"Using first column '{first_column}' as company_name")
                    df.rename(columns={first_column: 'company_name'}, inplace=True)
                    # Save the modified file
                    df.to_csv(filepath, index=False)
                else:
                    raise ValueError("CSV file has no columns")
            
            # Add necessary columns based on extraction type
            if extraction_type == 'cin' or extraction_type == 'both':
                if 'cin' not in df.columns:
                    df['cin'] = ''
                if 'cin_status' not in df.columns:
                    df['cin_status'] = ''
            
            if extraction_type == 'turnover' or extraction_type == 'both':
                if 'turnover' not in df.columns:
                    df['turnover'] = ''
                if 'turnover_status' not in df.columns:
                    df['turnover_status'] = ''
            
            if 'status' not in df.columns:
                df['status'] = ''
            
            # Save the updated dataframe with new columns
            df.to_csv(filepath, index=False)
            
            # Always use background processing to avoid timeouts
            task_id = str(int(time.time()))
            
            # Calculate estimated time (more time if extracting both CIN and turnover)
            total_companies = len(df)
            if extraction_type == 'both':
                avg_time_per_company = 20  # seconds (for both CIN and turnover)
            else:
                avg_time_per_company = 12  # seconds (for single extraction)
                
            estimated_total_seconds = total_companies * avg_time_per_company
            
            # Calculate estimated completion time
            start_time = datetime.now()
            estimated_completion_time = start_time + timedelta(seconds=estimated_total_seconds)
            
            # Create a task for tracking
            processing_tasks[task_id] = {
                'status': 'queued',
                'filepath': filepath,
                'timestamp': start_time.strftime("%Y-%m-%d %H:%M:%S"),
                'total_companies': total_companies,
                'processed_companies': 0,
                'current_company': '',
                'start_time': start_time.isoformat(),
                'estimated_completion_time': estimated_completion_time.isoformat(),
                'estimated_total_seconds': estimated_total_seconds,
                'avg_time_per_company': avg_time_per_company,
                'task_type': 'cin',  # Keep this as 'cin' for compatibility with existing code
                'extraction_type': extraction_type  # Add the new extraction type field
            }
            
            # Queue the processing task
            processing_queue.put((task_id, filepath))
            
            # Store the task ID in session
            session['task_id'] = task_id
            
            # Redirect to processing page
            return redirect(url_for('processing', task_id=task_id))
        except Exception as e:
            logger.exception("Error processing file")
            flash(f'Error processing file: {str(e)}', 'danger')
            return redirect(url_for('cin_extractor'))
    else:
        flash('File type not allowed. Please upload a CSV file.', 'danger')
        return redirect(url_for('cin_extractor'))

def save_checkpoint(task_id, df, current_index, global_count):
    """Save checkpoint data for potential recovery with enhanced error handling"""
    if not task_id:
        return None
    
    checkpoint_file = os.path.join(CHECKPOINT_DIR, f'checkpoint_{task_id}.csv')
    
    # Multiple retry attempts for transient file system errors
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            # Try to save the dataframe to CSV
            try:
                df.to_csv(checkpoint_file, index=False)
            except Exception as csv_err:
                logger.warning(f"Error saving checkpoint CSV on attempt {attempt+1}: {csv_err}")
                # Wait and retry
                time.sleep(retry_delay * (attempt + 1))
                if attempt == max_retries - 1:  # Last attempt
                    raise
                continue
            
            # Calculate progress percentage
            total_rows = len(df)
            progress = int((current_index / total_rows) * 100) if total_rows > 0 else 0
            
            # Save metadata about the checkpoint
            metadata = {
                'task_id': task_id,
                'timestamp': datetime.now().isoformat(),
                'current_index': current_index,
                'total_rows': total_rows,
                'global_search_count': global_count,
                'processed_companies': current_index,
                'progress_percentage': progress,
                'checkpoint_file': checkpoint_file
            }
            
            # Try to save the metadata JSON
            metadata_file = os.path.join(CHECKPOINT_DIR, f'metadata_{task_id}.json')
            try:
                with open(metadata_file, 'w') as f:
                    import json
                    json.dump(metadata, f)
            except Exception as json_err:
                logger.warning(f"Error saving metadata JSON on attempt {attempt+1}: {json_err}")
                # Wait and retry
                time.sleep(retry_delay * (attempt + 1))
                if attempt == max_retries - 1:  # Last attempt
                    raise
                continue
            
            # Both files successfully saved
            logger.debug(f"Checkpoint saved at index {current_index} for task {task_id} on attempt {attempt+1}")
            
            # Store the checkpoint info in the task info for error recovery
            if task_id in processing_tasks:
                current_time = datetime.now()
                processing_tasks[task_id]['last_checkpoint_file'] = checkpoint_file
                processing_tasks[task_id]['last_checkpoint_index'] = current_index
                processing_tasks[task_id]['last_checkpoint_time'] = current_time.isoformat()
                processing_tasks[task_id]['last_updated'] = current_time.isoformat()
                processing_tasks[task_id]['progress'] = progress
                processing_tasks[task_id]['checkpoint_metadata'] = metadata
                
                # Update the task status to ensure it reflects the correct progress
                if processing_tasks[task_id]['status'] == 'error':
                    # If we're saving a checkpoint but status is 'error',
                    # we should update to 'interrupted' so it can be resumed
                    if progress > 0:
                        processing_tasks[task_id]['status'] = 'interrupted'
                        processing_tasks[task_id]['interrupted_at'] = current_time.isoformat()
                        logger.info(f"Task {task_id} set to 'interrupted' from 'error' since we have checkpoint data (progress: {progress}%)")
                
            return checkpoint_file
            
        except Exception as e:
            if attempt < max_retries - 1:
                logger.warning(f"Retrying checkpoint save (attempt {attempt+1}/{max_retries}): {e}")
                time.sleep(retry_delay * (attempt + 1))
            else:
                logger.exception(f"Failed to save checkpoint for task {task_id} after {max_retries} attempts: {e}")
                # Even if we failed to save the checkpoint, update the task info with what we know
                if task_id in processing_tasks:
                    processing_tasks[task_id]['checkpoint_error'] = str(e)
                    processing_tasks[task_id]['last_updated'] = datetime.now().isoformat()
                    # Mark task as having an error if not already
                    if processing_tasks[task_id]['status'] not in ['error', 'interrupted']:
                        processing_tasks[task_id]['status'] = 'error'
                        processing_tasks[task_id]['error'] = f"Failed to save checkpoint: {str(e)}"
                return None
    
    # Should never reach here, but just in case
    return None

def load_checkpoint(task_id):
    """Load checkpoint data for recovery with enhanced error handling"""
    checkpoint_file = os.path.join(CHECKPOINT_DIR, f'checkpoint_{task_id}.csv')
    metadata_file = os.path.join(CHECKPOINT_DIR, f'metadata_{task_id}.json')
    
    if not os.path.exists(checkpoint_file) or not os.path.exists(metadata_file):
        logger.error(f"Checkpoint files not found for task {task_id}")
        return None, None
    
    # Multiple retry attempts for transient file system errors
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            # Try to load the dataframe from CSV
            try:
                df = pd.read_csv(checkpoint_file)
            except Exception as csv_err:
                logger.warning(f"Error reading checkpoint CSV on attempt {attempt+1}: {csv_err}")
                # Wait and retry
                time.sleep(retry_delay * (attempt + 1))
                if attempt == max_retries - 1:  # Last attempt
                    raise
                continue
            
            # Try to load the metadata JSON
            try:
                with open(metadata_file, 'r') as f:
                    import json
                    metadata = json.load(f)
            except Exception as json_err:
                logger.warning(f"Error reading metadata JSON on attempt {attempt+1}: {json_err}")
                # Wait and retry
                time.sleep(retry_delay * (attempt + 1))
                if attempt == max_retries - 1:  # Last attempt
                    raise
                continue
                
            # If we get here, both files loaded successfully
            logger.info(f"Checkpoint for task {task_id} loaded successfully on attempt {attempt+1}")
            return df, metadata
            
        except Exception as e:
            if attempt < max_retries - 1:
                logger.warning(f"Retrying checkpoint load (attempt {attempt+1}/{max_retries})")
                time.sleep(retry_delay * (attempt + 1))
            else:
                logger.exception(f"Failed to load checkpoint for task {task_id} after {max_retries} attempts: {e}")
                return None, None
    
    # Should never reach here, but just in case
    return None, None

def process_csv_in_background(filepath, resume_from_task_id=None, resume_index=None):
    """Process the CSV file in a background thread to avoid request timeouts"""
    # Get the task ID from the current processing task
    current_task_id = None
    for task_id, task_info in processing_tasks.items():
        if task_info.get('filepath') == filepath and task_info['status'] in ['queued', 'processing', 'interrupted']:
            current_task_id = task_id
            break
    
    # Determine if we're resuming from a checkpoint
    resuming = False
    if resume_from_task_id and resume_index is not None:
        # Try to load checkpoint data
        checkpoint_df, metadata = load_checkpoint(resume_from_task_id)
        if checkpoint_df is not None and metadata is not None:
            df = checkpoint_df
            current_task_id = resume_from_task_id
            start_index = resume_index
            # Set global counter (need to access before declaring global)
            global global_search_count
            # Get search count from checkpoint
            if 'global_search_count' in metadata:
                global_search_count = metadata['global_search_count']
            logger.info(f"Resuming from checkpoint at index {start_index} (task {resume_from_task_id})")
            resuming = True
    
    # If not resuming, start fresh
    if not resuming:
        # Read the CSV file
        try:
            df = pd.read_csv(filepath)
        except Exception:
            # Try again with different encoding
            df = pd.read_csv(filepath, encoding='latin1')
        
        # Check if 'company_name' column exists
        if 'company_name' not in df.columns:
            # If not, use the first column
            company_name_column = df.columns[0]
            df.rename(columns={company_name_column: 'company_name'}, inplace=True)
        
        # Create result columns
        if 'cin' not in df.columns:
            df['cin'] = ''
        if 'status' not in df.columns:
            df['status'] = ''
        
        start_index = 0
    
    # Process each company name
    total_rows = len(df)
    
    # Track time metrics for accurate remaining time calculation
    time_per_company = []
    
    # If task was interrupted, update its status to processing
    if current_task_id and processing_tasks[current_task_id]['status'] == 'interrupted':
        processing_tasks[current_task_id]['status'] = 'processing'
        processing_tasks[current_task_id]['resume_timestamp'] = datetime.now().isoformat()
    
    # Keep track of the last checkpoint file
    last_checkpoint_file = None
    
    # Process starting from the appropriate index
    for index in range(start_index, total_rows):
        row = df.iloc[index]
        company_name = row['company_name']
        company_start_time = time.time()
        logger.debug(f"Processing {company_name} ({index+1}/{total_rows})")
        
        # Update progress info if we have a task ID
        if current_task_id:
            processing_tasks[current_task_id]['processed_companies'] = index
            processing_tasks[current_task_id]['current_company'] = company_name
            processing_tasks[current_task_id]['current_index'] = index
            
            # Calculate remaining time based on average time per company
            if index > start_index and time_per_company:
                avg_time = sum(time_per_company) / len(time_per_company)
                remaining_companies = total_rows - index
                remaining_seconds = avg_time * remaining_companies
                
                # Update estimated completion time
                now = datetime.now()
                estimated_completion_time = now + timedelta(seconds=remaining_seconds)
                
                # Update task info
                processing_tasks[current_task_id]['remaining_seconds'] = int(remaining_seconds)
                processing_tasks[current_task_id]['remaining_time_formatted'] = str(timedelta(seconds=int(remaining_seconds)))
                processing_tasks[current_task_id]['estimated_completion_time'] = estimated_completion_time.isoformat()
                processing_tasks[current_task_id]['progress_percentage'] = int((index / total_rows) * 100)
                processing_tasks[current_task_id]['avg_time_per_company'] = round(avg_time, 2)
        
        # Skip already processed companies when resuming
        if resuming and df.at[index, 'status'] in ['Success', 'Not found'] and df.at[index, 'status'] != 'Processing':
            logger.debug(f"Skipping already processed company {company_name}")
            continue
        
        try:
            # Use the human-like delay behavior from our scraper module
            # This will automatically add realistic random delays (3-20 seconds) with human-like variations
            
            # Get the extraction type from task info
            extraction_type = EXTRACT_CIN  # Default to CIN extraction
            if current_task_id and 'extraction_type' in processing_tasks[current_task_id]:
                extraction_type = processing_tasks[current_task_id]['extraction_type']
            
            # Use search engine rotation strategy
            current_search_count = global_search_count
            
            # Initialize variables
            cin = None
            cin_status = 'Not found'
            turnover = None
            turnover_status = 'Not found'
            overall_status = 'Not found'
            
            # Extract CIN if requested
            if extraction_type == EXTRACT_CIN or extraction_type == EXTRACT_BOTH:
                # Search for the CIN number using the rotation between Yahoo and Bing
                global_search_count += 1
                cin = search_for_cin(company_name, current_search_count)
                
                # Log which search engine we're on
                if current_search_count % SEARCH_ENGINE_ROTATION_COUNT == 0 and current_search_count > 0:
                    logger.info(f"Switching search engines after {SEARCH_ENGINE_ROTATION_COUNT} searches (current count: {current_search_count})")
                
                # Verify CIN format - it should be 21 characters with specific pattern
                # CIN Pattern: [Letter][5 digits][2 letters][4 digits][3 letters][6 digits]
                cin_pattern = r'^[A-Z][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$'
                
                if cin and re.match(cin_pattern, cin):
                    df.at[index, 'cin'] = cin
                    df.at[index, 'cin_status'] = 'Success'
                    cin_status = 'Success'
                    overall_status = 'Success'
                    logger.debug(f"Successfully found CIN for {company_name}: {cin}")
                else:
                    df.at[index, 'cin_status'] = 'Not found'
                    cin_status = 'Not found'
                    logger.debug(f"No valid CIN found for {company_name}")
                    
                # If we're only extracting CIN, update overall status
                if extraction_type == EXTRACT_CIN:
                    df.at[index, 'status'] = cin_status
            
            # Extract turnover if requested (and add a slight delay between searches)
            if extraction_type == EXTRACT_TURNOVER or extraction_type == EXTRACT_BOTH:
                # Add a small delay between CIN and turnover searches to reduce detection risk
                if extraction_type == EXTRACT_BOTH:
                    human_delay()
                
                # Search for turnover data using the same rotation strategy
                global_search_count += 1
                turnover = search_for_turnover(company_name, current_search_count + 1)  # Use a different count
                
                if turnover:
                    df.at[index, 'turnover'] = turnover
                    df.at[index, 'turnover_status'] = 'Success'
                    turnover_status = 'Success'
                    overall_status = 'Success'  # Mark as success if we found turnover
                    logger.debug(f"Successfully found turnover for {company_name}: {turnover}")
                else:
                    df.at[index, 'turnover_status'] = 'Not found'
                    turnover_status = 'Not found'
                    logger.debug(f"No turnover data found for {company_name}")
                
                # If we're only extracting turnover, update overall status
                if extraction_type == EXTRACT_TURNOVER:
                    df.at[index, 'status'] = turnover_status
            
            # For "both" mode, set overall status
            if extraction_type == EXTRACT_BOTH:
                if cin_status == 'Success' or turnover_status == 'Success':
                    df.at[index, 'status'] = 'Success'
                else:
                    df.at[index, 'status'] = 'Not found'
            
            # Store company data in the database with retry logic
            max_retries = 3
            retry_delay = 1
            
            for attempt in range(max_retries):
                try:
                    # Create a new app context for each database operation
                    with app.app_context():
                        # Verify CIN format if we have one
                        cin_pattern = r'^[A-Z][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$'
                        valid_cin = cin if cin and re.match(cin_pattern, cin) else None
                        
                        new_company = Company(
                            company_name=company_name,
                            cin=valid_cin,
                            turnover=turnover,
                            cin_status=cin_status,
                            turnover_status=turnover_status,
                            status=overall_status,
                            source_file=os.path.basename(filepath),
                            user_ip=request.remote_addr if request else None,
                            extraction_type=extraction_type
                        )
                        db.session.add(new_company)
                        db.session.commit()
                        logger.debug(f"Company {company_name} added to database")
                        break  # Success, exit retry loop
                    
                except Exception as e:
                    if "SSL connection has been closed" in str(e) or "connection" in str(e).lower():
                        # Connection issue, retry after delay
                        logger.warning(f"Database connection error on attempt {attempt+1}/{max_retries}: {str(e)}")
                        
                        # Close the session to release the connection
                        try:
                            db.session.rollback()
                            db.session.close()
                        except:
                            pass
                        
                        # Wait before retry
                        time.sleep(retry_delay * (attempt + 1))
                        
                        # If last attempt failed
                        if attempt == max_retries - 1:
                            logger.error(f"Failed to add company {company_name} to database after {max_retries} attempts")
                    else:
                        # Other error, just log and continue
                        logger.exception(f"Error storing company in database: {str(e)}")
                        break  # Don't retry for non-connection errors
                
        except Exception as e:
            logger.exception(f"Error searching for {company_name}")
            df.at[index, 'status'] = f'Error: {str(e)}'
            
            # Store error in database with retry logic
            max_retries = 3
            retry_delay = 1
            
            for attempt in range(max_retries):
                try:
                    # Create a new app context for each database operation
                    with app.app_context():
                        new_company = Company(
                            company_name=company_name,
                            cin=None,
                            status=f'Error: {str(e)}'[:50],  # Truncate to fit in the database field
                            source_file=os.path.basename(filepath),
                            user_ip=request.remote_addr if request else None
                        )
                        db.session.add(new_company)
                        db.session.commit()
                        break  # Success, exit retry loop
                    
                except Exception as db_err:
                    if "SSL connection has been closed" in str(db_err) or "connection" in str(db_err).lower():
                        # Connection issue, retry after delay
                        logger.warning(f"Database connection error on attempt {attempt+1}/{max_retries}: {str(db_err)}")
                        
                        # Close the session to release the connection
                        try:
                            db.session.rollback()
                            db.session.close()
                        except:
                            pass
                        
                        # Wait before retry
                        time.sleep(retry_delay * (attempt + 1))
                        
                        # If last attempt failed
                        if attempt == max_retries - 1:
                            logger.error(f"Failed to add error record for {company_name} to database after {max_retries} attempts")
                    else:
                        # Other error, just log and continue
                        logger.exception(f"Error storing company error in database: {str(db_err)}")
                        break  # Don't retry for non-connection errors
            
            # If we get too many errors, mark as interrupted and save checkpoint
            # This allows auto-recovery after a cooldown period
            if current_task_id and "detected as automated" in str(e).lower():
                logger.warning("Bot detection triggered - interrupting task to avoid blocking")
                processing_tasks[current_task_id]['status'] = 'interrupted'
                processing_tasks[current_task_id]['error'] = str(e)
                processing_tasks[current_task_id]['interrupted_at'] = datetime.now().isoformat()
                processing_tasks[current_task_id]['resume_index'] = index
                processing_tasks[current_task_id]['resume_after'] = 315  # seconds (about 5.25 minutes)
                
                # Save full checkpoint for recovery
                last_checkpoint_file = save_checkpoint(current_task_id, df, index, global_search_count)
                
                # Save current progress for download with appropriate filename
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                
                # Set partial results filename based on extraction type
                if 'extraction_type' in processing_tasks[current_task_id]:
                    extraction_type = processing_tasks[current_task_id]['extraction_type']
                    if extraction_type == EXTRACT_CIN:
                        progress_filename = f'companies_with_cin_{timestamp}_partial.csv'
                    elif extraction_type == EXTRACT_TURNOVER:
                        progress_filename = f'companies_with_turnover_{timestamp}_partial.csv'
                    else:  # Both
                        progress_filename = f'companies_with_data_{timestamp}_partial.csv'
                else:
                    # Default to CIN if extraction type not found
                    progress_filename = f'companies_with_cin_{timestamp}_partial.csv'
                
                progress_path = os.path.join(app.config['OUTPUT_FOLDER'], progress_filename)
                df.to_csv(progress_path, index=False)
                
                # Update task info with partial results
                processing_tasks[current_task_id]['partial_results'] = progress_filename
                
                # Return partial results
                return progress_filename
        
        # Record time taken for this company
        company_time = time.time() - company_start_time
        time_per_company.append(company_time)
        
        # Periodically save progress to file and checkpoint
        if index % 5 == 0 or index == total_rows - 1:
            # Save progress with appropriate filename based on extraction type
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # Set progress filename based on extraction type
            if current_task_id and 'extraction_type' in processing_tasks[current_task_id]:
                extraction_type = processing_tasks[current_task_id]['extraction_type']
                if extraction_type == EXTRACT_CIN:
                    progress_filename = f'companies_with_cin_{timestamp}_progress.csv'
                elif extraction_type == EXTRACT_TURNOVER:
                    progress_filename = f'companies_with_turnover_{timestamp}_progress.csv'
                else:  # Both
                    progress_filename = f'companies_with_data_{timestamp}_progress.csv'
            else:
                # Default to CIN if extraction type not found
                progress_filename = f'companies_with_cin_{timestamp}_progress.csv'
                
            progress_path = os.path.join(app.config['OUTPUT_FOLDER'], progress_filename)
            df.to_csv(progress_path, index=False)
            
            # Save checkpoint for recovery
            last_checkpoint_file = save_checkpoint(current_task_id, df, index, global_search_count)
    
    # Create final output filename with timestamp based on extraction type
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # Create appropriate filename based on extraction type
    if current_task_id and 'extraction_type' in processing_tasks[current_task_id]:
        extraction_type = processing_tasks[current_task_id]['extraction_type']
        if extraction_type == EXTRACT_CIN:
            output_filename = f'companies_with_cin_{timestamp}.csv'
        elif extraction_type == EXTRACT_TURNOVER:
            output_filename = f'companies_with_turnover_{timestamp}.csv'
        else:  # Both
            output_filename = f'companies_with_data_{timestamp}.csv'
    else:
        # Default to CIN if extraction type not found
        output_filename = f'companies_with_cin_{timestamp}.csv'
        
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    
    # Before saving, remove duplicate companies to ensure we only have one row per company
    df_deduplicated = df.drop_duplicates(subset=['company_name'], keep='first')
    
    # Save the deduplicated results
    df_deduplicated.to_csv(output_path, index=False)
    
    # Update final statistics if we have a task ID
    if current_task_id:
        processing_tasks[current_task_id]['processed_companies'] = total_rows
        processing_tasks[current_task_id]['progress_percentage'] = 100
        processing_tasks[current_task_id]['remaining_seconds'] = 0
        processing_tasks[current_task_id]['avg_time_per_company'] = round(sum(time_per_company) / len(time_per_company), 2) if time_per_company else 0
    
    return output_filename

def process_csv(filepath):
    """Queue files for background processing"""
    # Read the CSV file to check format
    try:
        df = pd.read_csv(filepath)
    except Exception:
        # Try again with different encoding
        df = pd.read_csv(filepath, encoding='latin1')
    
    # Remove duplicate company names before processing
    original_count = len(df)
    
    # Check if company_name column exists first
    if 'company_name' in df.columns:
        # Remove duplicate company names
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        duplicate_count = original_count - len(df)
        if duplicate_count > 0:
            logger.info(f"Removed {duplicate_count} duplicate company entries from input file")
            flash(f"Removed {duplicate_count} duplicate company entries from your input file.", 'info')
    
    # Check if 'company_name' column exists, if not use the first column
    if 'company_name' not in df.columns:
        logger.debug(f"No 'company_name' column found. Available columns: {df.columns.tolist()}")
        if len(df.columns) > 0:
            first_column = df.columns[0]
            logger.debug(f"Using first column '{first_column}' as company_name")
            df.rename(columns={first_column: 'company_name'}, inplace=True)
            
            # Now that we have a company_name column, check for duplicates
            original_count = len(df)
            df = df.drop_duplicates(subset=['company_name'], keep='first')
            duplicate_count = original_count - len(df)
            if duplicate_count > 0:
                logger.info(f"Removed {duplicate_count} duplicate company entries after column rename")
                flash(f"Removed {duplicate_count} duplicate company entries from your input file.", 'info')
            
            # Save the modified file
            df.to_csv(filepath, index=False)
        else:
            raise ValueError("CSV file has no columns")
    
    # Create task ID for tracking
    task_id = str(int(time.time()))
    
    # Calculate estimated time based on average processing time (about 12 seconds per company)
    total_companies = len(df)
    avg_time_per_company = 12  # seconds
    estimated_total_seconds = total_companies * avg_time_per_company
    
    # Calculate estimated completion time
    start_time = datetime.now()
    estimated_completion_time = start_time + timedelta(seconds=estimated_total_seconds)
    
    processing_tasks[task_id] = {
        'status': 'queued',
        'filepath': filepath,
        'timestamp': start_time.strftime("%Y-%m-%d %H:%M:%S"),
        'total_companies': total_companies,
        'processed_companies': 0,
        'start_time': start_time.isoformat(),
        'estimated_completion_time': estimated_completion_time.isoformat(),
        'estimated_total_seconds': estimated_total_seconds,
        'avg_time_per_company': avg_time_per_company
    }
    
    # Add to the processing queue
    processing_queue.put((task_id, filepath))
    
    # Create an initial output file with status
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # Get extraction type from task info
    extraction_type = processing_tasks[task_id].get('extraction_type', 'cin')
    
    # Create appropriate filename and headers based on extraction type
    if extraction_type == 'cin':
        output_filename = f'companies_with_cin_{timestamp}_processing.csv'
        headers = ['company_name', 'cin', 'status']
    elif extraction_type == 'turnover':
        output_filename = f'companies_with_turnover_{timestamp}_processing.csv'
        headers = ['company_name', 'turnover', 'status']
    else:  # Both
        output_filename = f'companies_with_data_{timestamp}_processing.csv'
        headers = ['company_name', 'cin', 'turnover', 'status']
    
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    
    # Create a basic CSV with appropriate headers and "Processing" status
    # First, deduplicate the company names to avoid duplicates in the initial processing file
    df_unique = df.drop_duplicates(subset=['company_name'], keep='first')
    
    with open(output_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        for _, row in df_unique.iterrows():
            # Create row with appropriate number of columns based on headers
            csv_row = [row['company_name']] + [''] * (len(headers) - 2) + ['Processing']
            writer.writerow(csv_row)
    
    # Store the task ID in the session
    session['task_id'] = task_id
    session['output_filename'] = output_filename
    
    return output_filename

@app.route('/cancel_task/<task_id>')
def cancel_task(task_id):
    """Cancel an ongoing task"""
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('task_history'))
    
    task_info = processing_tasks[task_id]
    
    # Allow canceling of any active task (processing, queued, or even interrupted/error)
    if task_info['status'] in ['completed', 'success', 'cancelled']:
        flash('Task cannot be cancelled - it is already complete or cancelled', 'warning')
        return redirect(url_for('task_history'))
    
    # Mark task as cancelled
    previous_status = task_info['status']
    task_info['status'] = 'cancelled'
    task_info['cancelled_at'] = datetime.now().isoformat()
    task_info['previous_status'] = previous_status
    
    # If we have partial results, note that in the task info
    if task_info.get('processed_companies', 0) > 0:
        task_info['has_partial_results'] = True
        
        # Regenerate partial results before exiting, similar to what we do when downloading partial results
        try:
            if task_info.get('filepath') and os.path.exists(task_info.get('filepath')):
                # Try to generate partial results file if possible
                if task_info.get('last_checkpoint_file') and os.path.exists(task_info['last_checkpoint_file']):
                    # Create a copy of the checkpoint file as a partial result
                    checkpoint_file = task_info['last_checkpoint_file']
                    timestamp = int(time.time())
                    partial_filename = f"partial_results_{task_id}_{timestamp}_cancelled.csv"
                    partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                    
                    # Copy the checkpoint file to the output folder
                    import shutil
                    shutil.copy2(checkpoint_file, partial_filepath)
                    
                    # Update task info with partial results
                    task_info['partial_results'] = partial_filename
                    task_info['cancelled_results'] = partial_filename
                    logger.info(f"Created partial results on cancellation for task {task_id}: {partial_filename}")
        except Exception as e:
            logger.error(f"Error generating partial results on task cancellation: {e}")
    
    logger.info(f"Task {task_id} cancelled by user. Previous status: {previous_status}")
    flash('Task cancelled successfully. Any processed results can be downloaded.', 'success')
    return redirect(url_for('task_history'))

@app.route('/resume_task/<task_id>')
def resume_task(task_id):
    """Resume a previously interrupted or error state task"""
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('cin_extractor'))
    
    task_info = processing_tasks[task_id]
    
    # Allow resuming from both interrupted and error states
    if task_info['status'] not in ['interrupted', 'error']:
        flash('Task cannot be resumed. It must be in interrupted or error state.', 'danger')
        return redirect(url_for('results'))
    
    # For error state, determine the best resume point
    if task_info['status'] == 'error':
        # If we have a last checkpoint index, use that
        if 'last_checkpoint_index' in task_info:
            resume_index = task_info['last_checkpoint_index']
        else:
            # Otherwise use the processed_companies count as the resume point
            resume_index = task_info.get('processed_companies', 0)
            
        # Update task info with our determined resume point
        task_info['resume_index'] = resume_index
        
        # Log the error recovery attempt
        logger.info(f"Attempting to recover from error state for task {task_id} at index {resume_index}")
    else:
        # For interrupted tasks, get the regular resume index
        resume_index = task_info.get('resume_index', 0)
    
    # Get the file path
    filepath = task_info.get('filepath')
    
    if not filepath or not os.path.exists(filepath):
        flash('Original file not found', 'danger')
        return redirect(url_for('cin_extractor'))
    
    # Update task status, preserving the previous error for history
    if task_info['status'] == 'error' and 'error' in task_info:
        task_info['previous_error'] = task_info['error']
        task_info['error_handled'] = True
        task_info['error_recovery_attempted'] = datetime.now().isoformat()
        del task_info['error']
    
    # Mark the task as processing again
    task_info['status'] = 'processing'
    task_info['resume_timestamp'] = datetime.now().isoformat()
    
    # Queue the task for resuming
    processing_queue.put((task_id, filepath, resume_index))
    
    # Notify the user
    flash('Processing resumed from last good checkpoint', 'success')
    
    flash('Task resuming from checkpoint', 'success')
    return redirect(url_for('processing', task_id=task_id))

@app.route('/download_partial/<task_id>')
def download_partial(task_id):
    """Download partial results from an in-progress or interrupted task"""
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('cin_extractor'))
    
    task_info = processing_tasks[task_id]
    
    # For completed tasks, use their output directly if available
    if task_info['status'] in ['completed', 'success'] and task_info.get('output_filename'):
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], task_info['output_filename'])
        if os.path.exists(output_path):
            # Generate a meaningful filename for the downloaded file based on extraction type
            extraction_type = task_info.get('extraction_type', 'cin')
            if extraction_type == 'cin':
                prefix = "cin_results"
            elif extraction_type == 'turnover':
                prefix = "turnover_results"
            else:  # both
                prefix = "company_data"
                
            download_name = f"{prefix}_{task_id}.csv"
            logger.info(f"Downloading completed results for task {task_id}")
            return send_file(output_path, as_attachment=True, download_name=download_name)
    
    # For any task status, try to generate or get partial results
    if task_info.get('processed_companies', 0) > 0:
        try:
            # Tell the task_status endpoint to regenerate partial results
            processing_tasks[task_id]['regenerate_partial'] = True
            
            # Force a call to task_status to regenerate partial results
            task_status_response = task_status(task_id)
            task_data = json.loads(task_status_response.get_data(as_text=True))
            
            # Get the updated partial results filename
            partial_results = task_data.get('partial_results')
            
            if not partial_results:
                # If task_status couldn't generate results, check if we have a checkpoint file
                if task_info.get('last_checkpoint_file') and os.path.exists(task_info['last_checkpoint_file']):
                    # Create a copy of the checkpoint file as a partial result
                    checkpoint_file = task_info['last_checkpoint_file']
                    timestamp = int(time.time())
                    partial_filename = f"partial_results_{task_id}_{timestamp}.csv"
                    partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                    
                    # Copy the checkpoint file to the output folder
                    import shutil
                    shutil.copy2(checkpoint_file, partial_filepath)
                    
                    # Update task info and use this as our partial results
                    partial_results = partial_filename
                    processing_tasks[task_id]['partial_results'] = partial_filename
                    logger.info(f"Created partial results from checkpoint for task {task_id}: {partial_filename}")
                else:
                    flash('Error generating partial results. Please try again.', 'warning')
                    return redirect(url_for('task_history'))
            
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], partial_results)
            
            # Generate a meaningful filename for the downloaded file
            processed_count = task_info.get('processed_companies', 0)
            total_companies = task_info.get('total_companies', 0)
            
            # Determine prefix based on extraction type
            extraction_type = task_info.get('extraction_type', 'cin')
            if extraction_type == 'cin':
                prefix = "cin_results"
            elif extraction_type == 'turnover':
                prefix = "turnover_results"
            else:  # both
                prefix = "company_data"
                
            if total_companies > 0:
                download_name = f"{prefix}_partial_{processed_count}_of_{total_companies}.csv"
            else:
                download_name = f"{prefix}_partial_{task_id}.csv"
            
            logger.info(f"Downloading partial results for task {task_id}: {partial_results}")
            return send_file(output_path, as_attachment=True, download_name=download_name)
            
        except Exception as e:
            logger.error(f"Error generating partial results for task {task_id}: {e}")
            flash(f'Error generating partial results: {str(e)}', 'danger')
            return redirect(url_for('task_history'))
    else:
        # Check if we have a filepath - we might be able to send the original file
        if task_info.get('filepath') and os.path.exists(task_info['filepath']):
            filepath = task_info['filepath']
            download_name = f"original_file_{task_id}.csv"
            logger.info(f"No processed data yet, downloading original file for task {task_id}")
            return send_file(filepath, as_attachment=True, download_name=download_name)
        else:
            flash('No results available yet. No companies have been processed.', 'warning')
            return redirect(url_for('task_history'))

@app.route('/processing/<task_id>')
def processing(task_id):
    """Show processing page for a specific task"""
    if task_id not in processing_tasks:
        flash('Task not found', 'danger')
        return redirect(url_for('cin_extractor'))
    
    return render_template('processing.html', task_id=task_id)

@app.route('/task_status/<task_id>')
def task_status(task_id):
    """API endpoint to check the status of a background task"""
    if task_id not in processing_tasks:
        return jsonify({'status': 'not_found'})
    
    task_info = processing_tasks[task_id].copy()  # Make a copy to avoid modifying the original
    
    # Calculate the progress percentage if not already set
    if 'progress' not in task_info and 'processed_companies' in task_info and 'total_companies' in task_info:
        if task_info['total_companies'] > 0:
            progress = int((task_info['processed_companies'] / task_info['total_companies']) * 100)
            task_info['progress'] = progress
    
    # Check if task is stalled/interrupted and not marked as such yet
    # First, verify that it's marked as processing
    if task_info['status'] == 'processing':
        # Check last update time, if it's too old, mark as interrupted
        if 'last_updated' in task_info:
            try:
                last_updated_str = task_info['last_updated']
                if isinstance(last_updated_str, str):
                    if last_updated_str.endswith('Z'):
                        last_updated_str = last_updated_str.replace('Z', '+00:00')
                    last_updated = datetime.fromisoformat(last_updated_str)
                elif isinstance(last_updated_str, datetime):
                    last_updated = last_updated_str
                else:
                    last_updated = datetime.now() - timedelta(hours=2)  # Default fallback
                
                # If no update for more than 5 minutes, mark as interrupted
                if (datetime.now() - last_updated).total_seconds() > 300:  # 5 minutes
                    task_info['status'] = 'interrupted'
                    processing_tasks[task_id]['status'] = 'interrupted'  # Update the original
                    processing_tasks[task_id]['interrupted_at'] = datetime.now().isoformat()
                    processing_tasks[task_id]['error'] = "Processing was interrupted due to inactivity. You can resume from where it stopped."
                    logger.warning(f"Task {task_id} automatically marked as interrupted due to inactivity")
            except Exception as e:
                logger.error(f"Error checking task staleness: {e}")
    
    # If task is 'processing', 'interrupted', or 'error', create partial results file if needed
    if task_info['status'] in ['processing', 'interrupted', 'error'] and task_info.get('processed_companies', 0) > 0:
        # Only generate partial results if we have processed at least one company
        if not task_info.get('partial_results') or task_info.get('regenerate_partial', False):
            try:
                # Generate partial results file with CIN data from database
                filepath = task_info.get('filepath')
                if filepath and os.path.exists(filepath):
                    # Read original CSV file with company names
                    df = pd.read_csv(filepath)
                    
                    # Get processed companies from the database with their CINs and statuses
                    processed_companies = []
                    
                    try:
                        # Query the database for all companies from this file
                        source_file = os.path.basename(filepath)
                        with app.app_context():
                            companies = db.session.query(Company).filter_by(source_file=source_file).all()
                            
                            for company in companies:
                                processed_companies.append({
                                    'company_name': company.company_name,
                                    'cin': company.cin,
                                    'status': company.status
                                })
                    except Exception as db_error:
                        logger.error(f"Database error while getting companies: {db_error}")
                    
                    # Create a DataFrame with the database results
                    if processed_companies:
                        results_df = pd.DataFrame(processed_companies)
                        
                        # Remove duplicates from database results by keeping only the most recent entry for each company
                        results_df = results_df.drop_duplicates(subset=['company_name'], keep='first')
                        
                        # Get unique company names from the original input file
                        df_unique = df.drop_duplicates(subset=['company_name'], keep='first')
                        
                        # Merge with the original file to include companies that haven't been processed yet
                        # This ensures we keep all original companies, and add CIN data to those processed
                        merged_df = pd.merge(df_unique, results_df, on='company_name', how='left')
                        
                        # Create partial results filename
                        timestamp = int(time.time())
                        partial_filename = f"partial_results_{task_id}_{timestamp}.csv"
                        partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                        
                        # Save merged results to file
                        merged_df.to_csv(partial_filepath, index=False)
                        
                        # Update task info with partial results filename
                        task_info['partial_results'] = partial_filename
                        processing_tasks[task_id]['partial_results'] = partial_filename
                        processing_tasks[task_id]['regenerate_partial'] = False
                        logger.info(f"Generated partial results for task {task_id}: {partial_filename}")
                    else:
                        # If no companies in database yet, just use the original file
                        # Create partial results filename
                        timestamp = int(time.time())
                        partial_filename = f"partial_results_{task_id}_{timestamp}.csv"
                        partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                        
                        # Save partial results to file
                        df.to_csv(partial_filepath, index=False)
                        
                        # Update task info with partial results filename
                        task_info['partial_results'] = partial_filename
                        processing_tasks[task_id]['partial_results'] = partial_filename
                        logger.info(f"Generated partial results for task {task_id} (no CIN data yet): {partial_filename}")
            except Exception as e:
                logger.error(f"Error generating partial results for task {task_id}: {e}")
    
    # If task is completed, update the output filename if available
    if task_info['status'] == 'completed' and 'output_filename' in task_info:
        session['output_filename'] = task_info['output_filename']
    
    # Check if interrupted task is ready to be resumed
    if task_info['status'] == 'interrupted':
        interrupted_at = task_info.get('interrupted_at')
        resume_after = task_info.get('resume_after', 315)  # Default to 315 seconds (5.25 min)
        
        if interrupted_at:
            interrupted_time = datetime.fromisoformat(interrupted_at)
            now = datetime.now()
            elapsed_seconds = (now - interrupted_time).total_seconds()
            
            # Calculate remaining time until auto-resume
            cooldown_remaining = max(0, resume_after - elapsed_seconds)
            task_info['cooldown_remaining'] = int(cooldown_remaining)
            task_info['auto_resume_in'] = str(timedelta(seconds=int(cooldown_remaining)))
            
            # Check if it's time to auto-resume
            if elapsed_seconds >= resume_after:
                task_info['can_resume'] = True
    
    return jsonify(task_info)

@app.route('/results')
def results():
    # Check if we have a task_id in the session
    task_id = session.get('task_id')
    
    # If there's a task_id and it's still processing, show the processing status
    if task_id and task_id in processing_tasks:
        task_info = processing_tasks[task_id]
        
        # Handle different task statuses
        if task_info['status'] in ['queued', 'processing']:
            return render_template('processing.html', task_id=task_id)
        elif task_info['status'] == 'interrupted':
            # If task was interrupted, show the processing page with interruption info
            return render_template('processing.html', task_id=task_id, interrupted=True)
        elif task_info['status'] == 'error':
            # If task had an error, show the processing page with error info and recovery options
            # Use the error recovery file if available
            if 'error_recovery_file' in task_info:
                session['output_filename'] = task_info['error_recovery_file']
                flash('Processing error occurred, but we saved your progress. You can download the partial results.', 'warning')
            elif 'partial_results' in task_info:
                session['output_filename'] = task_info['partial_results']
                flash('Processing error occurred, but we saved partial results. You can download what was processed so far.', 'warning')
            return render_template('processing.html', task_id=task_id, error=True, error_message=task_info.get('error', 'Unknown error'))
        elif task_info['status'] in ['completed', 'success'] and 'output_filename' in task_info:
            # Update the output filename for completed tasks
            session['output_filename'] = task_info['output_filename']
        # For any other status, check if we have partial results we can show
        elif 'partial_results' in task_info:
            session['output_filename'] = task_info['partial_results']
            flash('Showing the latest available results.', 'info')
    
    # Get the output filename from the session
    output_filename = session.get('output_filename')
    
    # If no output filename in session but we have a task_id, try to get results from it
    if not output_filename and task_id and task_id in processing_tasks:
        task_info = processing_tasks[task_id]
        
        # Try to get results from various possible sources
        if 'output_filename' in task_info:
            output_filename = task_info['output_filename']
            session['output_filename'] = output_filename
        elif 'partial_results' in task_info:
            output_filename = task_info['partial_results']
            session['output_filename'] = output_filename
        elif 'last_checkpoint_file' in task_info:
            # Generate a partial results file from the checkpoint
            try:
                checkpoint_file = task_info['last_checkpoint_file']
                if os.path.exists(checkpoint_file):
                    timestamp = int(time.time())
                    partial_filename = f"partial_results_{task_id}_{timestamp}.csv"
                    partial_filepath = os.path.join(app.config['OUTPUT_FOLDER'], partial_filename)
                    
                    # Copy the checkpoint file to the output folder
                    import shutil
                    shutil.copy2(checkpoint_file, partial_filepath)
                    
                    output_filename = partial_filename
                    session['output_filename'] = output_filename
                    flash('Showing results from the latest checkpoint.', 'info')
            except Exception as e:
                logger.error(f"Error creating results from checkpoint: {e}")
    
    # If we still don't have an output filename, look for the most recent partial results file
    if not output_filename:
        try:
            # Check for partial results files in the output folder
            output_files = sorted([f for f in os.listdir(app.config['OUTPUT_FOLDER']) if f.startswith('partial_results_')], 
                                key=lambda x: os.path.getmtime(os.path.join(app.config['OUTPUT_FOLDER'], x)), 
                                reverse=True)
            
            if output_files:
                output_filename = output_files[0]
                session['output_filename'] = output_filename
                flash('Showing the most recent results file. This may not be from your current session.', 'warning')
            else:
                # No output files found
                flash('No processed file found. Please run a new extraction.', 'danger')
                return redirect(url_for('cin_extractor'))
        except Exception as e:
            logger.error(f"Error finding most recent results file: {e}")
            flash('No processed file found. Please run a new extraction.', 'danger')
            return redirect(url_for('cin_extractor'))
    
    # Read the output file to display on the page
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    try:
        if not os.path.exists(output_path):
            flash(f'Results file not found. Please run a new extraction.', 'danger')
            return redirect(url_for('cin_extractor'))
            
        df = pd.read_csv(output_path)
        
        # Remove duplicate rows by company_name (keep the first occurrence)
        original_count = len(df)
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        duplicate_count = original_count - len(df)
        
        if duplicate_count > 0:
            flash(f'Removed {duplicate_count} duplicate company entries from results.', 'info')
        
        results = df.to_dict('records')
        
        # Calculate statistics
        stats = {
            'total': len(df),
            'success': sum(1 for row in results if row.get('status') == 'Success'),
            'not_found': sum(1 for row in results if row.get('status') == 'Not found'),
            'error': sum(1 for row in results if isinstance(row.get('status'), str) and row.get('status').startswith('Error'))
        }
        
        is_processing = output_filename.endswith('_processing.csv')
        return render_template('results.html', results=results, stats=stats, filename=output_filename, is_processing=is_processing, task_id=task_id)
    except Exception as e:
        logger.exception(f"Error reading results file: {output_path}")
        flash(f'Error reading results: {str(e)}', 'danger')
        return redirect(url_for('cin_extractor'))

@app.route('/download')
def download():
    output_filename = session.get('output_filename')
    
    # If no output filename in session, check if we have a task_id
    if not output_filename:
        task_id = session.get('task_id')
        if task_id and task_id in processing_tasks:
            task_info = processing_tasks[task_id]
            
            # Try to find a suitable file to download
            if 'output_filename' in task_info:
                output_filename = task_info['output_filename']
                session['output_filename'] = output_filename
            elif 'partial_results' in task_info:
                output_filename = task_info['partial_results']
                session['output_filename'] = output_filename
            else:
                # Try to find the most recent partial results file
                try:
                    output_files = sorted([f for f in os.listdir(app.config['OUTPUT_FOLDER']) if f.startswith('partial_results_')], 
                                      key=lambda x: os.path.getmtime(os.path.join(app.config['OUTPUT_FOLDER'], x)), 
                                      reverse=True)
                    
                    if output_files:
                        output_filename = output_files[0]
                        session['output_filename'] = output_filename
                except Exception as e:
                    logger.error(f"Error finding most recent results file: {e}")
    
    # If we still don't have an output filename, show error
    if not output_filename:
        flash('No processed file found', 'danger')
        return redirect(url_for('cin_extractor'))
    
    # Get the path to the output file
    output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
    
    # Check if the file exists
    if not os.path.exists(output_path):
        flash(f'Results file not found. Please run a new extraction.', 'danger')
        return redirect(url_for('cin_extractor'))
    
    try:
        # Read the file and ensure it has no duplicates
        df = pd.read_csv(output_path)
        
        # Remove any duplicates by company name - keep only the first occurrence
        if 'company_name' in df.columns:
            original_count = len(df)
            df_deduplicated = df.drop_duplicates(subset=['company_name'], keep='first')
            duplicate_count = original_count - len(df_deduplicated)
            
            # If we found and removed duplicates, create a new deduplicated file
            if duplicate_count > 0:
                logger.info(f"Removed {duplicate_count} duplicate entries from {output_filename}")
                
                # Determine extraction type from task info
                extraction_type = 'cin'  # Default to CIN
                task_id = session.get('task_id')
                if task_id and task_id in processing_tasks:
                    extraction_type = processing_tasks[task_id].get('extraction_type', 'cin')
                    
                # Set appropriate file prefix based on extraction type
                if extraction_type == 'cin':
                    prefix = "companies_with_cin"
                elif extraction_type == 'turnover':
                    prefix = "companies_with_turnover"
                else:  # both
                    prefix = "companies_with_data"
                    
                # Create a new file for the deduplicated data with a timestamp
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                clean_filename = f"{prefix}_deduplicated_{timestamp}.csv"
                clean_file_path = os.path.join(app.config['OUTPUT_FOLDER'], clean_filename)
                
                # Save the deduplicated data to the new file
                df_deduplicated.to_csv(clean_file_path, index=False)
                
                # Use the deduplicated file for download
                output_path = clean_file_path
                output_filename = clean_filename
                
                # Update session with new deduplicated file
                session['output_filename'] = clean_filename
        else:
            # If there's no company_name column, just use the original file
            logger.warning(f"No 'company_name' column found in {output_filename}, cannot deduplicate")
        
        # Set a meaningful download filename
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # Determine extraction type from task info
        extraction_type = 'cin'  # Default to CIN
        task_id = session.get('task_id')
        if task_id and task_id in processing_tasks:
            extraction_type = processing_tasks[task_id].get('extraction_type', 'cin')
            
        # Set appropriate file prefix based on extraction type
        if extraction_type == 'cin':
            prefix = "companies_with_cin"
        elif extraction_type == 'turnover':
            prefix = "companies_with_turnover"
        else:  # both
            prefix = "companies_with_data"
            
        # Create the download name with the appropriate prefix
        if "partial" in output_filename:
            download_name = f"{prefix}_partial_{timestamp}.csv"
        else:
            download_name = f"{prefix}_{timestamp}.csv"
        
        # Special case for tasks still in progress
        if task_id and task_id in processing_tasks:
            task_info = processing_tasks[task_id]
            
            if task_info.get('status') == 'processing':
                # For in-progress tasks, add more detail to the filename
                processed = task_info.get('processed_companies', 0)
                total = task_info.get('total_companies', 0)
                
                if total > 0:
                    percent = int((processed / total) * 100)
                    download_name = f"{prefix}_partial_{processed}_of_{total}_{percent}percent_{timestamp}.csv"
            
        return send_file(output_path, as_attachment=True, download_name=download_name)
    except Exception as e:
        logger.exception(f"Error processing download file {output_filename}")
        flash(f'Error preparing download: {str(e)}', 'danger')
        return redirect(url_for('cin_extractor'))

@app.route('/admin/companies', methods=['GET'])
def admin_companies():
    """
    Admin page to view all companies that have been processed.
    This is hidden from normal users and allows access to the entire database of companies.
    """
    # In a real application, we would implement proper authentication here
    # For now, we're just providing the route
    
    page = request.args.get('page', 1, type=int)
    per_page = 100  # Show 100 companies per page
    
    try:
        with app.app_context():
            # Get total count of companies with error handling
            try:
                company_count = db.session.query(Company).count()
            except Exception as e:
                logger.error(f"Error counting companies: {e}")
                if "connection" in str(e).lower():
                    # Try to reconnect to the database
                    db.session.rollback()
                    db.session.close()
                    # Wait a second and try again
                    time.sleep(1)
                    company_count = db.session.query(Company).count()
                else:
                    raise
            
            # Get paginated companies, ordered by most recent first
            try:
                companies = db.session.query(Company).order_by(Company.processed_at.desc())\
                    .limit(per_page).offset((page-1)*per_page).all()
            except Exception as e:
                logger.error(f"Error retrieving companies: {e}")
                if "connection" in str(e).lower():
                    # Try to reconnect to the database
                    db.session.rollback()
                    db.session.close()
                    # Wait a second and try again
                    time.sleep(1)
                    companies = db.session.query(Company).order_by(Company.processed_at.desc())\
                        .limit(per_page).offset((page-1)*per_page).all()
                else:
                    raise
            
            # Calculate pagination info
            total_pages = (company_count // per_page) + (1 if company_count % per_page > 0 else 0)
            
            return render_template('admin_companies.html', 
                                companies=companies, 
                                page=page, 
                                total_pages=total_pages,
                                company_count=company_count)
    except Exception as e:
        logger.exception(f"Error in admin_companies: {e}")
        flash(f"Error retrieving company data: {str(e)}", "danger")
        return render_template('admin_companies.html', 
                            companies=[], 
                            page=1, 
                            total_pages=1,
                            company_count=0,
                            error=str(e))

@app.route('/admin/export_all', methods=['GET'])
def admin_export_all():
    """Export all companies in the database to a CSV file"""
    # In a real application, we would implement proper authentication here
    
    try:
        with app.app_context():
            # Get all companies with error handling
            try:
                companies = db.session.query(Company).all()
            except Exception as e:
                logger.error(f"Error retrieving all companies: {e}")
                if "connection" in str(e).lower():
                    # Try to reconnect to the database
                    db.session.rollback()
                    db.session.close()
                    # Wait a second and try again
                    time.sleep(1)
                    companies = db.session.query(Company).all()
                else:
                    raise
            
            if not companies:
                flash('No companies found in the database', 'warning')
                return redirect(url_for('admin_companies'))
            
            # Create a DataFrame from the companies
            data = [{
                'company_name': company.company_name,
                'cin': company.cin,
                'status': company.status,
                'source_file': company.source_file,
                'user_ip': company.user_ip,
                'processed_at': company.processed_at
            } for company in companies]
            
            df = pd.DataFrame(data)
            
            # Remove duplicate company entries, keeping the most recent one
            df_deduplicated = df.drop_duplicates(subset=['company_name'], keep='first')
            
            # Log if duplicates were removed
            if len(df_deduplicated) < len(df):
                logger.info(f"Admin export: Removed {len(df) - len(df_deduplicated)} duplicate entries")
            
            # Create output file
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_filename = f'all_companies_{timestamp}.csv'
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
            
            # Save the deduplicated data to CSV
            df_deduplicated.to_csv(output_path, index=False)
            
            return send_file(output_path, as_attachment=True, download_name=output_filename)
    except Exception as e:
        logger.exception(f"Error in admin_export_all: {e}")
        flash(f"Error exporting company data: {str(e)}", "danger")
        return redirect(url_for('admin_companies'))

# Start the background worker thread at the end of the file to ensure all dependencies are loaded
# This MUST be at module level (not inside if __name__ == "__main__":) for Gunicorn to work
try:
    background_thread = threading.Thread(target=background_worker, daemon=True)
    background_thread.start()

    # Use multiple logging methods to ensure visibility in Render logs
    logger.warning("🚀 BACKGROUND WORKER THREAD STARTED SUCCESSFULLY 🚀")
    print("🚀 BACKGROUND WORKER THREAD STARTED SUCCESSFULLY 🚀")

    # Verify thread is actually alive
    if background_thread.is_alive():
        logger.warning("✅ Background worker thread is confirmed ALIVE and RUNNING")
        print("✅ Background worker thread is confirmed ALIVE and RUNNING")
    else:
        logger.error("❌ Background worker thread failed to start properly")
        print("❌ Background worker thread failed to start properly")

except Exception as e:
    logger.error(f"💥 CRITICAL: Failed to start background worker thread: {e}")
    print(f"💥 CRITICAL: Failed to start background worker thread: {e}")
    raise  # Re-raise to make deployment fail if worker can't start
