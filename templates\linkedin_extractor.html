{% extends 'layout.html' %}

{% block title %}LinkedIn Extractor - Coming Soon{% endblock %}

{% block extra_css %}
<style>
    .coming-soon-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }

    .coming-soon-content {
        text-align: center;
        color: white;
        z-index: 2;
        position: relative;
    }

    .coming-soon-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        animation: fadeInUp 1s ease-out;
    }

    .coming-soon-subtitle {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .coming-soon-description {
        font-size: 1.1rem;
        margin-bottom: 3rem;
        opacity: 0.8;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .linkedin-icon {
        font-size: 8rem;
        margin-bottom: 2rem;
        animation: bounce 2s infinite;
        color: #0077b5;
        text-shadow: 0 0 30px rgba(0, 119, 181, 0.5);
    }

    .progress-bar-container {
        width: 100%;
        max-width: 400px;
        margin: 0 auto 2rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 4px;
        animation: fadeInUp 1s ease-out 0.9s both;
    }

    .progress-bar-fill {
        height: 8px;
        background: linear-gradient(90deg, #00d4ff, #0077b5);
        border-radius: 25px;
        width: 0%;
        animation: progressFill 3s ease-in-out infinite;
    }

    .back-button {
        animation: fadeInUp 1s ease-out 1.2s both;
    }

    .floating-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
    }

    .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    .particle:nth-child(1) { width: 20px; height: 20px; left: 10%; animation-delay: 0s; }
    .particle:nth-child(2) { width: 15px; height: 15px; left: 20%; animation-delay: 1s; }
    .particle:nth-child(3) { width: 25px; height: 25px; left: 30%; animation-delay: 2s; }
    .particle:nth-child(4) { width: 18px; height: 18px; left: 40%; animation-delay: 3s; }
    .particle:nth-child(5) { width: 22px; height: 22px; left: 50%; animation-delay: 4s; }
    .particle:nth-child(6) { width: 16px; height: 16px; left: 60%; animation-delay: 5s; }
    .particle:nth-child(7) { width: 24px; height: 24px; left: 70%; animation-delay: 0.5s; }
    .particle:nth-child(8) { width: 19px; height: 19px; left: 80%; animation-delay: 1.5s; }
    .particle:nth-child(9) { width: 21px; height: 21px; left: 90%; animation-delay: 2.5s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-20px);
        }
        60% {
            transform: translateY(-10px);
        }
    }

    @keyframes progressFill {
        0% { width: 0%; }
        50% { width: 75%; }
        100% { width: 0%; }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.1;
        }
        50% {
            transform: translateY(-100px) rotate(180deg);
            opacity: 0.3;
        }
    }

    .pulse-glow {
        animation: pulseGlow 2s ease-in-out infinite alternate;
    }

    @keyframes pulseGlow {
        from {
            text-shadow: 0 0 20px rgba(0, 119, 181, 0.5);
        }
        to {
            text-shadow: 0 0 30px rgba(0, 119, 181, 0.8), 0 0 40px rgba(0, 119, 181, 0.6);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="coming-soon-container">
                <!-- Floating Particles -->
                <div class="floating-particles">
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                </div>

                <!-- Main Content -->
                <div class="coming-soon-content">
                    <div class="linkedin-icon pulse-glow">
                        <i class="fab fa-linkedin"></i>
                    </div>

                    <h1 class="coming-soon-title">Coming Soon</h1>
                    <h2 class="coming-soon-subtitle">LinkedIn Profile Extractor</h2>

                    <p class="coming-soon-description">
                        We're working hard to bring you an amazing LinkedIn profile extraction tool.
                        This feature will help you find LinkedIn profiles automatically from company names and person details.
                        Stay tuned for something incredible!
                    </p>

                    <div class="progress-bar-container">
                        <div class="progress-bar-fill"></div>
                    </div>

                    <div class="back-button">
                        <a href="{{ url_for('index') }}" class="btn btn-light btn-lg px-5 py-3">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to CIN Extractor
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}