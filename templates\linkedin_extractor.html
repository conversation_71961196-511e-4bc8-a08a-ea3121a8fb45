{% extends 'layout.html' %}

{% block title %}LinkedIn Extractor{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-light">
                <div class="card-header">
                    <h3>LinkedIn Profile Extractor</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary">
                        <h4 class="alert-heading">How it works:</h4>
                        <ol>
                            <li>Upload a CSV file with <strong>"Person Name"</strong> and <strong>"Current Company Name"</strong> columns (optional <strong>"Designation"</strong> column)</li>
                            <li>The tool will search for each person's LinkedIn profile using smart search algorithms</li>
                            <li>Processing happens in the background - you'll see real-time progress</li>
                            <li>When complete, you'll receive a CSV with LinkedIn profile URLs for all found profiles</li>
                        </ol>
                        <p class="mb-0">Larger files may take time to process. You can safely close this page and return later - progress is automatically saved.</p>
                    </div>

                    <form method="POST" action="{{ url_for('upload_linkedin_file') }}" enctype="multipart/form-data" class="mb-4">
                        <div class="mb-3">
                            <label for="file" class="form-label">Select CSV file:</label>
                            <input type="file" class="form-control" id="file" name="file" accept=".csv" required>
                            <div class="form-text text-light">
                                File must be in CSV format with "Person Name" and "Current Company Name" columns. Add an optional "Designation" column for better search results.
                            </div>
                        </div>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i> Upload and Process
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3 mb-4">
                        <p class="text-muted mb-2">First time user? Try with a sample file:</p>
                        <a href="{{ url_for('static', filename='sample_linkedin.csv') }}" download class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-file-download me-1"></i> Download Sample CSV
                        </a>
                    </div>

                    <div class="card bg-dark border-secondary mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">Tips for Best Results</h5>
                        </div>
                        <div class="card-body">
                            <ul class="text-light">
                                <li>Ensure names are formatted correctly (First Last) for better matching</li>
                                <li>Company names should be official names as they appear on LinkedIn</li>
                                <li>Including job titles in the "Designation" column significantly improves search accuracy</li>
                                <li>For large files (1000+ entries), consider splitting into smaller batches</li>
                                <li>The tool rotates between multiple search engines for better results</li>
                                <li>If processing is interrupted, you can resume from the last checkpoint</li>
                            </ul>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-dark border-info mb-3">
                                <div class="card-header bg-info text-dark">
                                    CSV Format Example
                                </div>
                                <div class="card-body">
                                    <pre class="text-light">Person Name,Current Company Name,Designation
John Smith,Acme Corporation,CEO
Jane Doe,XYZ Technologies,CTO
...</pre>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-dark border-warning">
                                <div class="card-header bg-warning text-dark">
                                    Output Example
                                </div>
                                <div class="card-body">
                                    <pre class="text-light">Person Name,Current Company Name,Designation,LinkedIn URL,Processing Status
John Smith,Acme Corporation,CEO,linkedin.com/in/john-smith-123,Success
Jane Doe,XYZ Technologies,CTO,,Not Found
...</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-muted text-center">
                    Created by Prashant
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}