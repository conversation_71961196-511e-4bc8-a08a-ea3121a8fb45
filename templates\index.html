{% extends 'layout.html' %}

{% block title %}Company Data Extractor - Upload{% endblock %}

{% block content %}
<div class="container py-4 py-md-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow animate-card">
                <div class="card-header bg-primary text-white p-3">
                    <h2 class="text-center mb-0 d-flex align-items-center justify-content-center">
                        <i class="bi bi-search me-2"></i>
                        <span>Company Data Extractor</span>
                    </h2>
                </div>
                <div class="card-body p-0">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show m-3" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close touch-friendly" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                        
                    <div class="p-3 p-md-4">
                        <p class="lead">Upload a CSV file containing company names to extract their Corporate Identification Numbers (CIN) and/or company turnover information.</p>
                        
                        <div class="accordion mb-4" id="instructionsAccordion">
                            <div class="accordion-item bg-dark border-0">
                                <h2 class="accordion-header" id="headingInstructions">
                                    <button class="accordion-button touch-friendly bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInstructions" aria-expanded="true" aria-controls="collapseInstructions">
                                        <i class="bi bi-info-circle me-2"></i>
                                        Instructions
                                    </button>
                                </h2>
                                <div id="collapseInstructions" class="accordion-collapse collapse show" aria-labelledby="headingInstructions">
                                    <div class="accordion-body">
                                        <p class="mb-0">
                                            Prepare a CSV file with a column named 'company_name' (or any column containing company names).
                                            <br><br>
                                            Upload the file using the form below and select what data you want to extract.
                                            <br><br>
                                            The application will extract the requested information and add it to your data.
                                            <br><br>
                                            You'll receive a downloadable CSV with the original data plus the extracted information.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" class="mt-4">
                            <div class="mb-4">
                                <label for="file" class="form-label fw-bold mb-2">Select CSV File</label>
                                <div class="file-upload-wrapper">
                                    <input class="form-control touch-friendly" type="file" id="file" name="file" accept=".csv" required>
                                </div>
                                <div class="form-text mt-2">
                                    <i class="bi bi-info-circle-fill me-1 text-primary"></i>
                                    Only CSV files are accepted. Maximum file size: 16MB.
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label fw-bold mb-2">Select Data to Extract</label>
                                <div class="d-flex flex-column flex-md-row gap-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="extraction_type" id="cin_only" value="cin" checked>
                                        <label class="form-check-label" for="cin_only">
                                            <i class="bi bi-card-text me-1"></i> CIN Only
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="extraction_type" id="turnover_only" value="turnover">
                                        <label class="form-check-label" for="turnover_only">
                                            <i class="bi bi-graph-up-arrow me-1"></i> Turnover Only
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="extraction_type" id="both" value="both">
                                        <label class="form-check-label" for="both">
                                            <i class="bi bi-layers me-1"></i> Both CIN & Turnover
                                        </label>
                                    </div>
                                </div>
                                <div class="form-text mt-2">
                                    <i class="bi bi-info-circle-fill me-1 text-primary"></i>
                                    Extracting both will take longer to process.
                                </div>
                            </div>
                            
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary btn-lg touch-friendly">
                                    <i class="bi bi-cloud-upload me-2"></i>
                                    Upload and Process
                                </button>
                            </div>
                        </form>
                        
                        <!-- Mobile-friendly sample file section -->
                        <div class="mt-4 text-center">
                            <p class="text-muted mb-2">First time user? Try with a sample file:</p>
                            <button type="button" class="btn btn-sm btn-outline-secondary touch-friendly" onclick="downloadSample()">
                                <i class="bi bi-file-earmark-arrow-down me-1"></i>
                                Download Sample CSV
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center text-muted p-3">
                    <small class="text-muted">Created by Prashant</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Create and download a sample CSV file
    function downloadSample() {
        const csvContent = "company_name\nTata Consultancy Services\nHDFC Bank\nReliance Industries\nInfosys Limited\nITC Limited";
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.setAttribute('href', url);
        a.setAttribute('download', 'sample_companies.csv');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }
    
    // Add touch ripple effect to buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Add visual feedback for touch interactions on mobile
        document.querySelectorAll('.btn, .touch-friendly').forEach(el => {
            el.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            }, { passive: true });
            
            el.addEventListener('touchend', function() {
                this.classList.remove('touch-active');
                
                // For buttons, add a slight delay before the action to show visual feedback
                if (this.classList.contains('btn') && !this.classList.contains('accordion-button')) {
                    this.classList.add('btn-pulsate');
                    setTimeout(() => {
                        this.classList.remove('btn-pulsate');
                    }, 300);
                }
            }, { passive: true });
        });
        
        // Enhance file input for mobile
        const fileInput = document.getElementById('file');
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    // Apply visual feedback when file is selected
                    const wrapper = this.closest('.file-upload-wrapper');
                    if (wrapper) {
                        wrapper.classList.add('file-selected');
                    }
                }
            });
        }
    });
</script>
{% endblock %}
