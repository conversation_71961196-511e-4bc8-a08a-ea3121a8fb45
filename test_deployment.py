#!/usr/bin/env python3
"""
Test script to validate deployment configuration
"""
import os
import sys
import importlib.util

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    # Test simplified app import
    try:
        import app_simple
        print("✓ app_simple.py imports successfully")
        app = app_simple.app
        print("✓ Flask app created successfully")
    except Exception as e:
        print(f"✗ Failed to import app_simple: {e}")
        return False
    
    # Test original app import (optional)
    try:
        import app
        print("✓ Original app.py imports successfully")
    except Exception as e:
        print(f"⚠ Original app.py import failed (expected): {e}")
    
    return True

def test_files():
    """Test if all required files exist"""
    print("\nTesting required files...")
    
    required_files = [
        'netlify.toml',
        '_redirects',
        'netlify/functions/app.py',
        'app_simple.py',
        'requirements.txt',
        'runtime.txt',
        'static/index.html'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_templates():
    """Test if template files exist"""
    print("\nTesting template files...")
    
    template_files = [
        'templates/layout.html',
        'templates/home.html',
        'templates/index.html',
        'templates/linkedin_extractor.html',
        'templates/404.html',
        'templates/500.html'
    ]
    
    all_exist = True
    for file_path in template_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_flask_app():
    """Test if Flask app can be created and basic routes work"""
    print("\nTesting Flask app functionality...")
    
    try:
        from app_simple import app
        
        with app.test_client() as client:
            # Test home page
            response = client.get('/')
            if response.status_code == 200:
                print("✓ Home page route works")
            else:
                print(f"✗ Home page returned status {response.status_code}")
                return False
            
            # Test CIN extractor page
            response = client.get('/cin-extractor')
            if response.status_code == 200:
                print("✓ CIN extractor route works")
            else:
                print(f"✗ CIN extractor returned status {response.status_code}")
                return False
            
            # Test health check
            response = client.get('/health')
            if response.status_code == 200:
                print("✓ Health check route works")
            else:
                print(f"✗ Health check returned status {response.status_code}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Flask app test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("CIN Extractor Deployment Configuration Test")
    print("=" * 50)
    
    tests = [
        ("File existence", test_files),
        ("Template files", test_templates),
        ("Import tests", test_imports),
        ("Flask app", test_flask_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n✓ All tests passed! Ready for Netlify deployment.")
        return 0
    else:
        print("\n✗ Some tests failed. Please fix issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
