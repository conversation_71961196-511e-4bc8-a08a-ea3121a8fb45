[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "beautifulsoup4>=4.13.3",
    "email-validator>=2.2.0",
    "flask>=3.1.0",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "pandas>=2.2.3",
    "psycopg2-binary>=2.9.10",
    "requests>=2.32.3",
    "selenium>=4.30.0",
    "trafilatura>=2.0.0",
    "webdriver-manager>=4.0.2",
    "werkzeug>=3.1.3",
]
