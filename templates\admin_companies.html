{% extends 'layout.html' %}

{% block title %}Admin - Companies Database{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card bg-dark text-light">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Companies Database (Admin Access)</h3>
                    <a href="{{ url_for('admin_export_all') }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> Export All Companies
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>Total Companies in Database:</strong> {{ company_count }}
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-dark table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Company Name</th>
                                    <th>CIN</th>
                                    <th>Status</th>
                                    <th>Source File</th>
                                    <th>User IP</th>
                                    <th>Processed At</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company in companies %}
                                <tr>
                                    <td>{{ company.id }}</td>
                                    <td>{{ company.company_name }}</td>
                                    <td>{{ company.cin or '—' }}</td>
                                    <td>
                                        {% if company.status == 'Success' %}
                                        <span class="badge bg-success">Success</span>
                                        {% elif company.status == 'Not found' %}
                                        <span class="badge bg-warning">Not Found</span>
                                        {% else %}
                                        <span class="badge bg-danger">{{ company.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ company.source_file or '—' }}</td>
                                    <td>{{ company.user_ip or '—' }}</td>
                                    <td>{{ company.processed_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">No companies found in the database</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if total_pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_companies', page=page-1) }}">Previous</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            {% endif %}
                            
                            {% for i in range(1, total_pages + 1) %}
                                {% if i == page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ i }}</span>
                                </li>
                                {% elif i <= 3 or i >= total_pages - 2 or (i >= page - 1 and i <= page + 1) %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_companies', page=i) }}">{{ i }}</a>
                                </li>
                                {% elif i == 4 and page > 5 or i == total_pages - 3 and page < total_pages - 4 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page < total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_companies', page=page+1) }}">Next</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
                <div class="card-footer text-muted text-center">
                    This is a secret admin page that allows you to view all companies processed by users.
                    <br>
                    <small>Created by Prashant</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}