import json
import os
import sys
from urllib.parse import unquote

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    # Try to import the simplified app first
    from app_simple import app
    print("Successfully imported simplified app")
except ImportError as e1:
    print(f"Failed to import simplified app: {e1}")
    try:
        # Fallback to original app
        from app import app
        print("Successfully imported original app")
    except ImportError as e2:
        print(f"Failed to import original app: {e2}")
        # Create a minimal Flask app as fallback
        from flask import Flask
        app = Flask(__name__)

        @app.route('/')
        def hello():
            return """
            <h1>CIN Extractor</h1>
            <p>Application is starting up...</p>
            <p>Import errors occurred:</p>
            <ul>
                <li>Simplified app: {}</li>
                <li>Original app: {}</li>
            </ul>
            <p>Please check the deployment logs.</p>
            """.format(str(e1), str(e2))

def handler(event, context):
    """
    Netlify Functions handler for Flask app
    """
    try:
        # Get the HTTP method and path
        http_method = event.get('httpMethod', 'GET')
        path = event.get('path', '/')
        
        # Handle query parameters
        query_params = event.get('queryStringParameters') or {}
        query_string = '&'.join([f"{k}={v}" for k, v in query_params.items()])
        
        # Handle request body
        body = event.get('body', '')
        if event.get('isBase64Encoded', False):
            import base64
            body = base64.b64decode(body).decode('utf-8')
        
        # Create a test client
        with app.test_client() as client:
            # Set up the request
            headers = {}
            if event.get('headers'):
                headers = event['headers']
            
            # Make the request
            if http_method == 'GET':
                response = client.get(path, query_string=query_string, headers=headers)
            elif http_method == 'POST':
                content_type = headers.get('content-type', 'application/x-www-form-urlencoded')
                response = client.post(path, data=body, headers=headers, content_type=content_type)
            elif http_method == 'PUT':
                response = client.put(path, data=body, headers=headers)
            elif http_method == 'DELETE':
                response = client.delete(path, headers=headers)
            else:
                response = client.get(path, query_string=query_string, headers=headers)
            
            # Prepare the response
            response_headers = {}
            for key, value in response.headers:
                response_headers[key] = value
            
            return {
                'statusCode': response.status_code,
                'headers': response_headers,
                'body': response.get_data(as_text=True),
                'isBase64Encoded': False
            }
    
    except Exception as e:
        print(f"Error in handler: {e}")
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'text/html'},
            'body': f'<h1>Internal Server Error</h1><p>Error: {str(e)}</p><p>Please check the deployment configuration.</p>',
            'isBase64Encoded': False
        }
